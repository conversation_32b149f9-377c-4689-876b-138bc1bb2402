"use client"

import { useState } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown, Eye, Edit, Trash2, BarChart3 } from "lucide-react"

import { <PERSON><PERSON> } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import { Checkbox } from "@/registry/new-york-v4/ui/checkbox"
import { DashboardChartTable } from "@/blocks/dashboard-chart-table"

// Sample data for the analytics dashboard
const analyticsData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Senior Manager",
    department: "Engineering",
    lastActivity: "2024-01-15T10:30:00Z",
    revenue: 15420.50,
    orders: 156,
    country: "United States"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Product Manager",
    department: "Product",
    lastActivity: "2024-01-14T16:45:00Z",
    revenue: 23850.75,
    orders: 203,
    country: "Canada"
  },
  {
    id: "3",
    name: "Michael Brown",
    email: "<EMAIL>",
    status: "inactive" as const,
    role: "Developer",
    department: "Engineering",
    lastActivity: "2024-01-10T09:15:00Z",
    revenue: 8920.25,
    orders: 98,
    country: "United Kingdom"
  },
  {
    id: "4",
    name: "Emily Davis",
    email: "<EMAIL>",
    status: "active" as const,
    role: "UX Designer",
    department: "Design",
    lastActivity: "2024-01-15T14:20:00Z",
    revenue: 12350.00,
    orders: 134,
    country: "Australia"
  },
  {
    id: "5",
    name: "David Chen",
    email: "<EMAIL>",
    status: "pending" as const,
    role: "Data Analyst",
    department: "Analytics",
    lastActivity: "2024-01-12T11:30:00Z",
    revenue: 19750.80,
    orders: 178,
    country: "Singapore"
  },
  {
    id: "6",
    name: "Lisa Garcia",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Marketing Manager",
    department: "Marketing",
    lastActivity: "2024-01-15T13:45:00Z",
    revenue: 27640.30,
    orders: 267,
    country: "Spain"
  },
  {
    id: "7",
    name: "James Anderson",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Full Stack Developer",
    department: "Engineering",
    lastActivity: "2024-01-15T08:30:00Z",
    revenue: 14280.45,
    orders: 142,
    country: "United States"
  },
  {
    id: "8",
    name: "Maria Rodriguez",
    email: "<EMAIL>",
    status: "inactive" as const,
    role: "Operations Coordinator",
    department: "Operations",
    lastActivity: "2024-01-08T15:20:00Z",
    revenue: 6780.90,
    orders: 67,
    country: "Mexico"
  },
  {
    id: "9",
    name: "Robert Taylor",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Engineering Lead",
    department: "Engineering",
    lastActivity: "2024-01-15T12:10:00Z",
    revenue: 31250.75,
    orders: 298,
    country: "United Kingdom"
  },
  {
    id: "10",
    name: "Jennifer Lee",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Customer Support Specialist",
    department: "Support",
    lastActivity: "2024-01-15T17:05:00Z",
    revenue: 9840.60,
    orders: 112,
    country: "South Korea"
  },
  {
    id: "11",
    name: "Christopher White",
    email: "<EMAIL>",
    status: "pending" as const,
    role: "Design Intern",
    department: "Design",
    lastActivity: "2024-01-13T10:45:00Z",
    revenue: 2450.25,
    orders: 35,
    country: "United States"
  },
  {
    id: "12",
    name: "Amanda Johnson",
    email: "<EMAIL>",
    status: "active" as const,
    role: "Sales Director",
    department: "Sales",
    lastActivity: "2024-01-15T16:30:00Z",
    revenue: 45680.90,
    orders: 412,
    country: "Canada"
  }
]

export interface UserData {
  id: string
  name: string
  email: string
  status: "active" | "inactive" | "pending"
  role: string
  department: string
  lastActivity: string
  revenue: number
  orders: number
  country: string
}

// Filter options for the table toolbar
const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Pending", value: "pending" },
]

const departmentOptions = [
  { label: "Engineering", value: "Engineering" },
  { label: "Product", value: "Product" },
  { label: "Design", value: "Design" },
  { label: "Marketing", value: "Marketing" },
  { label: "Analytics", value: "Analytics" },
  { label: "Operations", value: "Operations" },
  { label: "Support", value: "Support" },
  { label: "Sales", value: "Sales" },
]

export default function AnalyticsDashboardPage() {
  const [selectedRows, setSelectedRows] = useState<UserData[]>([])

  // Handle row selection
  const handleRowClick = (row: UserData) => {
    console.log("Row clicked:", row)
  }

  // Handle row deletion
  const handleDeleteRow = (row: UserData) => {
    console.log("Delete row:", row)
    // Implement delete logic here
  }

  // Status badge renderer with blue color system compliance
  const StatusBadge = ({ status }: { status: UserData["status"] }) => {
    const colors = {
      active: "bg-blue-100 text-blue-700 border-blue-200",
      inactive: "bg-gray-100 text-gray-800 border-gray-200",
      pending: "bg-blue-50 text-blue-600 border-blue-200",
    }

    return (
      <Badge
        className={colors[status]}
        data-test-id={`analytics-dashboard_status-badge-${status}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Table column definitions
  const columns: ColumnDef<UserData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="focus:ring-blue-500 focus:border-blue-500"
          data-test-id="analytics-dashboard_select-all-checkbox"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="focus:ring-blue-500 focus:border-blue-500"
          data-test-id={`analytics-dashboard_select-checkbox-${row.original.id}`}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium text-left justify-start hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard_name-sort-button"
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium text-gray-900" data-test-id={`analytics-dashboard_name-${row.original.id}`}>
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => (
        <div className="text-gray-600" data-test-id={`analytics-dashboard_email-${row.original.id}`}>
          {row.getValue("email")}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => (
        <div className="text-gray-700" data-test-id={`analytics-dashboard_role-${row.original.id}`}>
          {row.getValue("role")}
        </div>
      ),
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => (
        <div className="text-gray-700" data-test-id={`analytics-dashboard_department-${row.original.id}`}>
          {row.getValue("department")}
        </div>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "revenue",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard_revenue-sort-button"
        >
          Revenue
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("revenue"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)

        return (
          <div className="text-right font-medium text-gray-900" data-test-id={`analytics-dashboard_revenue-${row.original.id}`}>
            {formatted}
          </div>
        )
      },
    },
    {
      accessorKey: "orders",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard_orders-sort-button"
        >
          Orders
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-right text-gray-700" data-test-id={`analytics-dashboard_orders-${row.original.id}`}>
          {row.getValue("orders")}
        </div>
      ),
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }) => (
        <div className="text-gray-700" data-test-id={`analytics-dashboard_country-${row.original.id}`}>
          {row.getValue("country")}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const user = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 hover:bg-blue-50"
                data-test-id={`analytics-dashboard_actions-${user.id}`}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-test-id={`analytics-dashboard_actions-menu-${user.id}`}>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
                className="focus:bg-blue-50 focus:text-blue-700"
                data-test-id={`analytics-dashboard_copy-id-${user.id}`}
              >
                Copy user ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="focus:bg-blue-50 focus:text-blue-700"
                data-test-id={`analytics-dashboard_view-${user.id}`}
              >
                <Eye className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              <DropdownMenuItem
                className="focus:bg-blue-50 focus:text-blue-700"
                data-test-id={`analytics-dashboard_edit-${user.id}`}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit user
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600 focus:bg-red-50"
                onClick={() => handleDeleteRow(user)}
                data-test-id={`analytics-dashboard_delete-${user.id}`}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
  ]

  return (
    <div className="space-y-6 p-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Analytics Dashboard</h1>
          <p className="text-base text-gray-600 mt-2">
            Comprehensive analytics with interactive charts, metrics, and detailed user data management
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            className="border-blue-300 text-blue-600 hover:bg-blue-50 hover:text-blue-700 focus:ring-blue-500"
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500">
            <BarChart3 className="mr-2 h-4 w-4" />
            Generate Analytics
          </Button>
        </div>
      </div>

      {/* Dashboard Chart Table Component */}
      <DashboardChartTable
        summaryData={{
          totalRevenue: 264671.85,
          revenueChange: 23.4,
          totalUsers: 2847,
          usersChange: 18.2,
          activeUsers: 634,
          activeChange: 12.1,
          conversionRate: 4.7,
          conversionChange: 15.3,
        }}
        tableProps={{
          columns,
          data: analyticsData,
          config: {
            searchColumn: "name",
            searchPlaceholder: "Search users by name...",
            pageSize: 10,
            enableRowSelection: true,
            enableColumnVisibility: true,
            showToolbar: true,
            showPagination: true,
          },
          toolbar: {
            filters: [
              {
                column: "status",
                title: "Status",
                options: statusOptions,
              },
              {
                column: "department",
                title: "Department",
                options: departmentOptions,
              },
            ],
            enableColumnVisibility: true,
            enableExport: true,
          },
          onRowClick: handleRowClick,
          onDeleteRow: handleDeleteRow,
          deleteConfirmTitle: "Delete User",
          deleteConfirmMessage: "Are you sure you want to delete this user? This action cannot be undone.",
        }}
        showCharts={true}
        chartVariant="default"
        sectionGap="lg"
        className="w-full"
        data-test-id="analytics-dashboard-component"
      />
    </div>
  )
}