"use client"

/**
 * Expense Management Page - Updated to use Dynamic Table Configuration
 *
 * This demonstrates financial data handling with DynamicTableView featuring:
 * - Currency formatting with automatic precision
 * - Date field rendering with smart formatting
 * - Status badges with predefined variants
 * - Category selection with enhanced filtering
 * - Workspace management with icon support
 *
 * Dynamic field advantages:
 * - Automatic currency formatting ($1,234.56)
 * - Smart date handling and display
 * - Consistent badge styling across status types
 * - Reduced code maintenance burden
 * - Type-specific filter generation
 */

import * as React from "react"
import { Plus, Receipt, DollarSign, Calendar, Building2 } from "lucide-react"

import { Button } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"

import { DynamicTableView, TableActionsDropdown, type DynamicTableConfig } from "@/components/table-view"
import { Crud<PERSON>reateDrawer, CrudViewDrawer, type DynamicFormConfig, type DynamicViewConfig } from "@/components/crud-drawers"
import { ExpenseData, ExpenseFormData, EXPENSE_CATEGORIES, EXPENSE_STATUSES, EXPENSE_WORKSPACES, EXPENSE_APPROVERS, EXPENSE_LIFECYCLE_STAGES, EXPENSE_LEAD_STATUSES, EXPENSE_CONTACT_OWNERS } from "@/types/expense"

const mockExpenses: ExpenseData[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneNumber: "$245.67", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Travel", // Maps to category
    leadStatus: "Approved", // Maps to status
    jobTitle: "Business Travel", // Maps to description
    merchant: "Marriott Hotel",
    amount: 245.67,
    date: "2024-01-15",
    category: "Travel",
    workspace: "Sales Department",
    tag: "Client Meeting",
    description: "Business travel accommodation for client meeting",
    receiptUrl: "/receipts/receipt-1.pdf",
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-16T10:30:00Z",
    approvedAt: "2024-01-16T10:30:00Z",
    notes: ["Approved by finance manager"]
  },
  {
    id: "2",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phoneNumber: "$89.50", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Meals & Entertainment", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Team Lunch", // Maps to description
    merchant: "The Grill Restaurant",
    amount: 89.50,
    date: "2024-01-20",
    category: "Meals & Entertainment",
    workspace: "Marketing Team",
    tag: "Team Building",
    description: "Monthly team lunch meeting",
    receiptUrl: "/receipts/receipt-2.pdf",
    createdAt: "2024-01-20T12:00:00Z",
    updatedAt: "2024-01-20T12:00:00Z",
    notes: ["Pending manager approval"]
  },
  {
    id: "3",
    firstName: "Bob",
    lastName: "Johnson",
    email: "<EMAIL>",
    phoneNumber: "$1,250.00", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Software & Subscriptions", // Maps to category
    leadStatus: "Processing", // Maps to status
    jobTitle: "Annual Software License", // Maps to description
    merchant: "Adobe Creative Suite",
    amount: 1250.00,
    date: "2024-01-18",
    category: "Software & Subscriptions",
    workspace: "Engineering Team",
    tag: "Development Tools",
    description: "Annual Adobe Creative Suite license renewal",
    receiptUrl: "/receipts/receipt-3.pdf",
    createdAt: "2024-01-18T14:00:00Z",
    updatedAt: "2024-01-19T09:00:00Z",
    notes: ["Under finance review"]
  },
  {
    id: "4",
    firstName: "Alice",
    lastName: "Brown",
    email: "<EMAIL>",
    phoneNumber: "$45.30", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Transportation", // Maps to category
    leadStatus: "Rejected", // Maps to status
    jobTitle: "Uber Ride", // Maps to description
    merchant: "Uber Technologies",
    amount: 45.30,
    date: "2024-01-12",
    category: "Transportation",
    workspace: "HR Department",
    tag: "Daily Commute",
    description: "Uber ride to office",
    receiptUrl: "/receipts/receipt-4.pdf",
    createdAt: "2024-01-12T08:30:00Z",
    updatedAt: "2024-01-13T11:00:00Z",
    rejectedAt: "2024-01-13T11:00:00Z",
    notes: ["Rejected - personal commute not reimbursable"]
  },
  {
    id: "5",
    firstName: "Charlie",
    lastName: "Wilson",
    email: "<EMAIL>",
    phoneNumber: "$156.78", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Office Supplies", // Maps to category
    leadStatus: "Reimbursed", // Maps to status
    jobTitle: "Office Equipment", // Maps to description
    merchant: "Staples",
    amount: 156.78,
    date: "2024-01-10",
    category: "Office Supplies",
    workspace: "Operations Team",
    tag: "Office Setup",
    description: "Desk organizers and office supplies",
    receiptUrl: "/receipts/receipt-5.pdf",
    createdAt: "2024-01-10T16:00:00Z",
    updatedAt: "2024-01-17T14:30:00Z",
    approvedAt: "2024-01-15T10:00:00Z",
    notes: ["Reimbursed via payroll"]
  }
]

export default function ExpenseManagementPage() {
  const [expenses, setExpenses] = React.useState<ExpenseData[]>(mockExpenses)
  const [crudDrawerOpen, setCrudDrawerOpen] = React.useState(false)
  const [selectedExpense, setSelectedExpense] = React.useState<ExpenseData | null>(null)

  const handleCreateExpense = (expenseData: Record<string, any>) => {
    if (selectedExpense) {
      // Update existing expense
      const updatedExpense: ExpenseData = {
        ...selectedExpense,
        ...expenseData,
        updatedAt: new Date().toISOString()
      }
      setExpenses(prev => prev.map(e => e.id === selectedExpense.id ? updatedExpense : e))
    } else {
      // Create new expense
      const newExpense: ExpenseData = {
        ...expenseData,
        id: Date.now().toString(),
        date: expenseData.date || new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        notes: []
      }
      setExpenses(prev => [...prev, newExpense])
    }
    setCrudDrawerOpen(false)
    setSelectedExpense(null)
  }

  const mapContactOwnerToWorkspace = (contactOwner: string): string => {
    const workspaceMap: { [key: string]: string } = {
      "<EMAIL>": "Finance Department",
      "<EMAIL>": "Sales Department",
      "<EMAIL>": "Marketing Team",
      "<EMAIL>": "HR Department",
      "<EMAIL>": "Executive Team"
    }
    return workspaceMap[contactOwner] || "General"
  }

  const handleViewExpense = (expense: ExpenseData) => {
    console.log("handleViewExpense called - opening CrudCreateDrawer in view mode", expense.merchant)
    setSelectedExpense(expense)
    setCrudDrawerOpen(true)
  }

  const handleAddExpense = () => {
    console.log("handleAddExpense called - opening CrudCreateDrawer in create mode")
    setSelectedExpense(null)
    setCrudDrawerOpen(true)
  }

  const handleDeleteExpense = (expense: ExpenseData) => {
    setExpenses(prev => prev.filter(e => e.id !== expense.id))
  }

  // Note: Badge styling is now handled automatically by the dynamic field system

  // Dynamic table configuration for enhanced expense management
  const expenseTableConfig: DynamicTableConfig<ExpenseData> = {
    fields: [
      {
        id: "avatarUrl",
        label: "Receipt",
        type: "avatar",
        size: "default",
        fallbackField: "merchant",
        showName: false,
        sortable: false,
        width: 60,
      },
      {
        id: "merchant",
        label: "Merchant",
        type: "text",
        required: true,
        sortable: true,
        filterable: true,
      },
      {
        id: "description",
        label: "Description",
        type: "text",
        sortable: true,
        filterable: true,
      },
      {
        id: "amount",
        label: "Amount",
        type: "number",
        format: "currency",
        currency: "USD",
        precision: 2,
        sortable: true,
        filterable: false, // Sensitive financial data
      },
      {
        id: "category",
        label: "Category",
        type: "select",
        options: EXPENSE_LIFECYCLE_STAGES.map(category => ({
          label: category,
          value: category,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "leadStatus",
        label: "Status",
        type: "badge",
        variants: {
          "Approved": { variant: "default" },
          "Reimbursed": { variant: "default" },
          "Rejected": { variant: "destructive" },
          "Pending Approval": { variant: "outline" },
          "Processing": { variant: "outline" },
          "Draft": { variant: "secondary" },
        },
        sortable: true,
        filterable: true,
      },
      {
        id: "workspace",
        label: "Workspace",
        type: "select",
        options: EXPENSE_WORKSPACES.map(workspace => ({
          label: workspace.label,
          value: workspace.label,
          icon: Building2,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "date",
        label: "Date",
        type: "date",
        format: "PPp",
        sortable: true,
        filterable: true,
      },
      {
        id: "actions",
        label: "Actions",
        type: "custom",
        cellRenderer: ({ row }) => (
          <TableActionsDropdown
            row={row as ExpenseData}
            onView={handleViewExpense}
            onEdit={handleViewExpense}
          />
        ),
        sortable: false,
        filterable: false,
        hideable: false,
        width: 100,
      },
    ],
    defaults: {
      sortable: true,
      filterable: true,
      hideable: true,
      defaultVisible: true,
    },
  }

  // Dynamic form configuration for expense creation/editing
  const expenseFormConfig: DynamicFormConfig = {
    title: "Expense Management",
    description: "Submit expense for reimbursement",
    submitLabel: "Save Expense",
    cancelLabel: "Cancel",
    createAnotherLabel: "Save and Add Another",
    sections: [
      {
        id: "expense",
        title: "Expense Details",
        fields: [
          {
            id: "merchant",
            label: "Merchant/Vendor",
            type: "text",
            required: true,
            placeholder: "Restaurant, store, vendor name"
          },
          {
            id: "amount",
            label: "Amount",
            type: "number",
            required: true,
            validation: {
              min: 0.01,
              max: 10000
            },
            helpText: "Enter amount in USD"
          },
          {
            id: "category",
            label: "Category",
            type: "select",
            required: true,
            options: EXPENSE_LIFECYCLE_STAGES.map(category => ({
              label: category,
              value: category
            }))
          },
          {
            id: "date",
            label: "Expense Date",
            type: "date",
            required: true
          },
          {
            id: "description",
            label: "Description",
            type: "textarea",
            placeholder: "Detailed description of the expense...",
            validation: {
              maxLength: 300
            }
          }
        ]
      },
      {
        id: "administrative",
        title: "Administrative Details",
        fields: [
          {
            id: "workspace",
            label: "Workspace/Department",
            type: "select",
            required: true,
            options: EXPENSE_WORKSPACES.map(workspace => ({
              label: workspace.label,
              value: workspace.label
            }))
          },
          {
            id: "contactOwner",
            label: "Approver/Manager",
            type: "select",
            required: true,
            options: EXPENSE_CONTACT_OWNERS.map(approver => ({
              label: approver,
              value: approver
            }))
          },
          {
            id: "leadStatus",
            label: "Status",
            type: "select",
            required: true,
            defaultValue: "Draft",
            options: EXPENSE_LEAD_STATUSES.map(status => ({
              label: status,
              value: status
            }))
          },
          {
            id: "tag",
            label: "Tag",
            type: "text",
            placeholder: "e.g., Client Meeting, Conference"
          },
          {
            id: "receiptUrl",
            label: "Receipt URL",
            type: "url",
            placeholder: "https://example.com/receipt.jpg",
            helpText: "Link to uploaded receipt"
          }
        ]
      }
    ]
  }

  // Dynamic view configuration for expense viewing
  const expenseViewConfig: DynamicViewConfig = {
    title: "Expense Details",
    header: {
      title: {
        fields: ["merchant", "description"]
      },
      badge: {
        field: "leadStatus",
        variants: {
          "Approved": { variant: "default", label: "Approved" },
          "Reimbursed": { variant: "default", label: "Reimbursed" },
          "Rejected": { variant: "destructive", label: "Rejected" },
          "Pending Approval": { variant: "outline", label: "Pending Approval" },
          "Processing": { variant: "outline", label: "Processing" },
          "Draft": { variant: "secondary", label: "Draft" }
        }
      }
    },
    sections: [
      {
        id: "expense",
        title: "Expense Information",
        fields: [
          {
            id: "amount",
            label: "Amount",
            renderer: "custom",
            customRenderer: (value) => `$${value?.toLocaleString()}`
          },
          {
            id: "category",
            label: "Category",
            renderer: "badge",
            variant: "outline"
          },
          {
            id: "date",
            label: "Date",
            renderer: "date"
          },
          {
            id: "description",
            label: "Description",
            renderer: "text"
          },
          {
            id: "tag",
            label: "Tag",
            renderer: "text"
          }
        ]
      },
      {
        id: "administrative",
        title: "Administrative Details",
        fields: [
          {
            id: "workspace",
            label: "Workspace",
            renderer: "text"
          },
          {
            id: "contactOwner",
            label: "Approver",
            renderer: "text"
          },
          {
            id: "createdAt",
            label: "Submitted",
            renderer: "date"
          },
          {
            id: "approvedAt",
            label: "Approved",
            renderer: "date"
          },
          {
            id: "receiptUrl",
            label: "Receipt",
            renderer: "url"
          }
        ]
      }
    ]
  }

  // Note: Toolbar configuration is now automatically generated from dynamic fields
  // This provides better consistency and reduces code duplication

  return (
    <div className="space-y-6 p-6" data-test-id="expense-management_page">
      <div className="flex items-center justify-between" data-test-id="expense-management_header">
        <div data-test-id="expense-management_title-section">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900" data-test-id="expense-management_title">Expense Management</h1>
          <p className="text-gray-600" data-test-id="expense-management_description">
            Manage expense submissions, approvals, and reimbursements
          </p>
        </div>
      </div>

      <DynamicTableView
        dynamicConfig={expenseTableConfig}
        data={expenses}
        config={{
          searchColumn: "merchant",
          searchPlaceholder: "Search expenses...",
          pageSize: 10,
          enableRowSelection: true,
          enableColumnVisibility: true,
          showToolbar: true,
          showPagination: true,
        }}
        toolbar={{
          enableExport: true,
          onExport: (format: "csv" | "excel" | "pdf") => {
            console.log(`Exporting expenses as ${format}`)
            // TODO: Implement export functionality
          },
          extraActions: (
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleAddExpense}
              data-test-id="expense-management_add-button"
            >
              <Plus className="mr-2 h-4 w-4" data-test-id="expense-management_add-icon" />
              Add Expense
            </Button>
          ),
        }}
        onRowClick={handleViewExpense}
        onDeleteRow={handleDeleteExpense}
        deleteConfirmTitle="Delete Expense"
        deleteConfirmMessage="Are you sure you want to delete this expense? This action cannot be undone and will remove all associated data."
        data-test-id="expense-management_table"
        testIdPrefix="expense-management"
      />

      {/* Single CrudCreateDrawer for both create and view/edit - Following Intent Layout Guidelines */}
      {crudDrawerOpen && (() => {
        console.log("CrudCreateDrawer is open", {
          mode: selectedExpense ? 'view/edit' : 'create',
          expense: selectedExpense?.merchant
        })
        return null
      })()}
      <CrudCreateDrawer
        isOpen={crudDrawerOpen}
        onClose={() => {
          console.log("CrudCreateDrawer onClose called")
          setCrudDrawerOpen(false)
          setSelectedExpense(null)
        }}
        dynamicConfig={expenseFormConfig}
        dynamicData={selectedExpense || undefined}
        onDynamicSubmit={handleCreateExpense}
        onDynamicEdit={handleCreateExpense}
        data-test-id="expense-management_form-drawer"
      />
    </div>
  )
}