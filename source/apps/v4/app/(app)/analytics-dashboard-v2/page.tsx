"use client"

import { useState } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown, Eye, Edit, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import { Checkbox } from "@/registry/new-york-v4/ui/checkbox"
import { DashboardChartTableV2 } from "@/blocks/dashboard-chart-table-v2"

// Sample analytics data
const tableData = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    role: "Manager",
    department: "Sales",
    lastActivity: "2024-01-15",
    revenue: 15420.50,
    orders: 12,
    country: "United States"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "active",
    role: "Developer",
    department: "Engineering",
    lastActivity: "2024-01-14",
    revenue: 8750.25,
    orders: 8,
    country: "Canada"
  },
  {
    id: "3",
    name: "Emily <PERSON>",
    email: "<EMAIL>",
    status: "pending",
    role: "Designer",
    department: "Design",
    lastActivity: "2024-01-13",
    revenue: 12300.75,
    orders: 15,
    country: "United Kingdom"
  },
  {
    id: "4",
    name: "James Wilson",
    email: "<EMAIL>",
    status: "inactive",
    role: "Analyst",
    department: "Analytics",
    lastActivity: "2024-01-10",
    revenue: 6890.00,
    orders: 5,
    country: "Australia"
  },
  {
    id: "5",
    name: "Lisa Brown",
    email: "<EMAIL>",
    status: "active",
    role: "Lead",
    department: "Marketing",
    lastActivity: "2024-01-15",
    revenue: 18200.30,
    orders: 22,
    country: "United States"
  }
]

export interface UserData {
  id: string
  name: string
  email: string
  status: "active" | "inactive" | "pending"
  role: string
  department: string
  lastActivity: string
  revenue: number
  orders: number
  country: string
}

// Convert to typed data
const data: UserData[] = tableData as UserData[]

// Filter options for the table toolbar
const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Pending", value: "pending" },
]

const departmentOptions = [
  { label: "Engineering", value: "Engineering" },
  { label: "Sales", value: "Sales" },
  { label: "Design", value: "Design" },
  { label: "Marketing", value: "Marketing" },
  { label: "Analytics", value: "Analytics" },
  { label: "Operations", value: "Operations" },
  { label: "Support", value: "Support" },
]

export default function AnalyticsDashboardV2Page() {
  const [selectedRows, setSelectedRows] = useState<UserData[]>([])

  // Handle row selection
  const handleRowClick = (row: UserData) => {
    console.log("Row clicked:", row)
  }

  // Handle row deletion
  const handleDeleteRow = (row: UserData) => {
    console.log("Delete row:", row)
    // Implement delete logic here
  }

  // Status badge renderer
  const StatusBadge = ({ status }: { status: UserData["status"] }) => {
    const colors = {
      active: "bg-blue-100 text-blue-700 border-blue-200",
      inactive: "bg-gray-100 text-gray-800 border-gray-200",
      pending: "bg-blue-50 text-blue-600 border-blue-200",
    }

    return (
      <Badge
        variant="outline"
        className={colors[status]}
        data-test-id={`analytics-dashboard-v2_status-badge-${status}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Table column definitions
  const columns: ColumnDef<UserData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="focus:ring-blue-500 focus:border-blue-500"
          data-test-id="analytics-dashboard-v2_select-all-checkbox"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="focus:ring-blue-500 focus:border-blue-500"
          data-test-id={`analytics-dashboard-v2_select-checkbox-${row.original.id}`}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium text-left justify-start hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard-v2_name-sort-button"
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium text-gray-900" data-test-id={`analytics-dashboard-v2_name-${row.original.id}`}>
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => (
        <div className="text-gray-600" data-test-id={`analytics-dashboard-v2_email-${row.original.id}`}>
          {row.getValue("email")}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => (
        <div className="text-gray-900" data-test-id={`analytics-dashboard-v2_role-${row.original.id}`}>
          {row.getValue("role")}
        </div>
      ),
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => (
        <div className="text-gray-900" data-test-id={`analytics-dashboard-v2_department-${row.original.id}`}>
          {row.getValue("department")}
        </div>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "revenue",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard-v2_revenue-sort-button"
        >
          Revenue
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("revenue"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)

        return (
          <div className="text-right font-medium text-gray-900" data-test-id={`analytics-dashboard-v2_revenue-${row.original.id}`}>
            {formatted}
          </div>
        )
      },
    },
    {
      accessorKey: "orders",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full hover:bg-blue-50 hover:text-blue-700"
          data-test-id="analytics-dashboard-v2_orders-sort-button"
        >
          Orders
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-right text-gray-900" data-test-id={`analytics-dashboard-v2_orders-${row.original.id}`}>
          {row.getValue("orders")}
        </div>
      ),
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }) => (
        <div className="text-gray-900" data-test-id={`analytics-dashboard-v2_country-${row.original.id}`}>
          {row.getValue("country")}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const user = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 hover:bg-blue-50"
                data-test-id={`analytics-dashboard-v2_actions-${user.id}`}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-test-id={`analytics-dashboard-v2_actions-menu-${user.id}`}>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
                data-test-id={`analytics-dashboard-v2_copy-id-${user.id}`}
              >
                Copy user ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem data-test-id={`analytics-dashboard-v2_view-${user.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              <DropdownMenuItem data-test-id={`analytics-dashboard-v2_edit-${user.id}`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit user
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteRow(user)}
                data-test-id={`analytics-dashboard-v2_delete-${user.id}`}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
  ]

  return (
    <div className="flex h-full flex-1 flex-col space-y-6 p-6" data-test-id="analytics-dashboard-v2-page">
      <div className="flex items-center justify-between space-y-2" data-test-id="analytics-dashboard-v2-page_header">
        <div data-test-id="analytics-dashboard-v2-page_title-section">
          <h1 className="text-3xl font-bold text-gray-900" data-test-id="analytics-dashboard-v2-page_title">
            Analytics Dashboard V2
          </h1>
          <p className="text-gray-600" data-test-id="analytics-dashboard-v2-page_description">
            Advanced analytics dashboard with pie chart visualizations and comprehensive data management
          </p>
        </div>
      </div>

      <DashboardChartTableV2
        summaryData={{
          totalRevenue: 61553.80,
          revenueChange: 23.4,
          totalUsers: 2,890,
          usersChange: 18.7,
          activeUsers: 687,
          activeChange: 12.3,
          conversionRate: 4.8,
          conversionChange: 15.2,
        }}
        tableProps={{
          columns,
          data,
          config: {
            searchColumn: "name",
            searchPlaceholder: "Search users...",
            pageSize: 10,
            enableRowSelection: true,
            enableColumnVisibility: true,
            showToolbar: true,
            showPagination: true,
          },
          toolbar: {
            filters: [
              {
                column: "status",
                title: "Status",
                options: statusOptions,
              },
              {
                column: "department",
                title: "Department",
                options: departmentOptions,
              },
            ],
            enableColumnVisibility: true,
            enableExport: true,
          },
          onRowClick: handleRowClick,
          onDeleteRow: handleDeleteRow,
          deleteConfirmTitle: "Delete User",
          deleteConfirmMessage: "Are you sure you want to delete this user? This action cannot be undone.",
        }}
        showCharts={true}
        chartVariant="default"
        sectionGap="lg"
        className="w-full"
        data-test-id="analytics-dashboard-v2-component"
      />
    </div>
  )
}