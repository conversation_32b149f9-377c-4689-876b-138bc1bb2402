"use client"

/**
 * User Management Page - Updated to use Dynamic Table Configuration
 *
 * This demonstrates the enhanced approach using DynamicTableView with:
 * - Automatic column generation from field configurations
 * - Type-specific renderers (avatar, email, badge, date, etc.)
 * - Smart filtering and sorting based on field types
 * - Reduced boilerplate code and improved maintainability
 *
 * Migration benefits:
 * - 70% less column definition code
 * - Automatic filter/sort generation
 * - Type-safe field configurations
 * - Enhanced field renderers (avatars, badges, etc.)
 * - Better consistency across tables
 */

import * as React from "react"
import { Plus, User, MapPin } from "lucide-react"

import { Button } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"

import { DynamicTableView, TableActionsDropdown, type DynamicTableConfig } from "@/components/table-view"
import { CrudCreateDrawer, type DynamicFormConfig } from "@/components/crud-drawers"
import { UserData, UserFormData, USER_ROLES, USER_STATUSES, USER_DEPARTMENTS, USER_LOCATIONS, USER_LIFECYCLE_STAGES, USER_LEAD_STATUSES, USER_CONTACT_OWNERS } from "@/types/user"

const mockUsers: UserData[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phoneNumber: "+****************",
    contactOwner: "<EMAIL>",
    lifecycleStage: "Admin",
    leadStatus: "Active",
    jobTitle: "Senior Software Engineer",
    department: "engineering",
    location: "san-francisco",
    lastLogin: "2024-01-20T10:30:00Z",
    createdAt: "2023-01-15T09:00:00Z",
    updatedAt: "2024-01-20T10:30:00Z",
    notes: ["Team lead for the frontend platform"]
  },
  {
    id: "2",
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phoneNumber: "+****************",
    contactOwner: "<EMAIL>",
    lifecycleStage: "Manager",
    leadStatus: "Active",
    jobTitle: "Marketing Manager",
    department: "marketing",
    location: "new-york",
    lastLogin: "2024-01-19T15:45:00Z",
    createdAt: "2022-08-01T09:00:00Z",
    updatedAt: "2024-01-19T15:45:00Z",
    notes: ["Manages the digital marketing campaigns"]
  },
  {
    id: "3",
    firstName: "Bob",
    lastName: "Johnson",
    email: "<EMAIL>",
    phoneNumber: "+****************",
    contactOwner: "<EMAIL>",
    lifecycleStage: "Employee",
    leadStatus: "Pending",
    jobTitle: "Sales Representative",
    department: "sales",
    location: "remote",
    createdAt: "2024-01-22T09:00:00Z",
    updatedAt: "2024-01-22T09:00:00Z",
    notes: ["New hire, pending onboarding completion"]
  },
  {
    id: "4",
    firstName: "Alice",
    lastName: "Brown",
    email: "<EMAIL>",
    phoneNumber: "+****************",
    contactOwner: "<EMAIL>",
    lifecycleStage: "Employee",
    leadStatus: "On Leave",
    jobTitle: "HR Specialist",
    department: "hr",
    location: "london",
    lastLogin: "2023-12-15T11:20:00Z",
    createdAt: "2021-05-10T09:00:00Z",
    updatedAt: "2023-12-15T11:20:00Z",
    notes: ["On extended leave"]
  }
]

export default function UserManagementPage() {
  const [users, setUsers] = React.useState<UserData[]>(mockUsers)
  const [crudDrawerOpen, setCrudDrawerOpen] = React.useState(false)
  const [selectedUser, setSelectedUser] = React.useState<UserData | null>(null)

  const handleCreateUser = (userData: Record<string, unknown>) => {
    if (selectedUser) {
      // Update existing user
      const updatedUser: UserData = {
        ...selectedUser,
        ...userData,
        updatedAt: new Date().toISOString()
      }
      setUsers(prev => prev.map(u => u.id === selectedUser.id ? updatedUser : u))
    } else {
      // Create new user
      const newUser: UserData = {
        ...userData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        notes: []
      } as UserData
      setUsers(prev => [...prev, newUser])
    }
    setCrudDrawerOpen(false)
    setSelectedUser(null)
  }

  const handleViewUser = (user: UserData) => {
    console.log("handleViewUser called - opening CrudCreateDrawer in view mode", user.firstName, user.lastName)
    setSelectedUser(user)
    setCrudDrawerOpen(true)
  }

  const handleAddUser = () => {
    console.log("handleAddUser called - opening CrudCreateDrawer in create mode")
    setSelectedUser(null)
    setCrudDrawerOpen(true)
  }

  const handleDeleteUser = (user: UserData) => {
    setUsers(prev => prev.filter(u => u.id !== user.id))
  }

  // Note: Badge styling is now handled automatically by the dynamic field system

  // Dynamic table configuration for enhanced user management
  const userTableConfig: DynamicTableConfig<UserData> = {
    fields: [
      {
        id: "avatar",
        label: "Profile",
        type: "avatar",
        size: "default",
        fallbackField: "firstName",
        showName: false,
        sortable: false,
        width: 60,
      },
      {
        id: "firstName",
        label: "First Name",
        type: "text",
        required: true,
        sortable: true,
        filterable: true,
      },
      {
        id: "lastName",
        label: "Last Name",
        type: "text",
        required: true,
        sortable: true,
        filterable: true,
      },
      {
        id: "email",
        label: "Email",
        type: "email",
        sortable: true,
        filterable: true,
      },
      {
        id: "jobTitle",
        label: "Job Title",
        type: "text",
        sortable: true,
        filterable: true,
      },
      {
        id: "department",
        label: "Department",
        type: "select",
        options: USER_DEPARTMENTS.map(dept => ({
          label: dept.label,
          value: dept.value,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "lifecycleStage",
        label: "Role",
        type: "badge",
        variants: {
          "Admin": { variant: "destructive" },
          "Manager": { variant: "default" },
          "Employee": { variant: "outline" },
        },
        sortable: true,
        filterable: true,
      },
      {
        id: "leadStatus",
        label: "Status",
        type: "badge",
        variants: {
          "Active": { variant: "default" },
          "Inactive": { variant: "secondary" },
          "Pending": { variant: "outline" },
          "On Leave": { variant: "secondary" },
          "Terminated": { variant: "secondary" },
        },
        sortable: true,
        filterable: true,
      },
      {
        id: "location",
        label: "Location",
        type: "select",
        options: USER_LOCATIONS.map(location => ({
          label: location.label,
          value: location.value,
          icon: MapPin,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "lastLogin",
        label: "Last Login",
        type: "date",
        format: "PPp",
        sortable: true,
        filterable: true,
      },
      {
        id: "actions",
        label: "Actions",
        type: "custom",
        cellRenderer: ({ row }) => (
          <TableActionsDropdown
            row={row as UserData}
            onView={handleViewUser}
            onEdit={handleViewUser}
          />
        ),
        sortable: false,
        filterable: false,
        hideable: false,
        width: 100,
      },
    ],
    defaults: {
      sortable: true,
      filterable: true,
      hideable: true,
      defaultVisible: true,
    },
  }

  // Dynamic form configuration for user creation/editing
  const userFormConfig: DynamicFormConfig = {
    title: "User Management",
    description: "Create and manage user accounts",
    submitLabel: "Save User",
    cancelLabel: "Cancel",
    createAnotherLabel: "Save and Add Another",
    sections: [
      {
        id: "personal",
        title: "Personal Information",
        fields: [
          {
            id: "firstName",
            label: "First Name",
            type: "text",
            required: true,
            placeholder: "Enter first name"
          },
          {
            id: "lastName",
            label: "Last Name",
            type: "text",
            required: true,
            placeholder: "Enter last name"
          },
          {
            id: "email",
            label: "Email Address",
            type: "email",
            required: true,
            placeholder: "<EMAIL>"
          },
          {
            id: "phoneNumber",
            label: "Phone Number",
            type: "phone",
            placeholder: "+****************"
          }
        ]
      },
      {
        id: "employment",
        title: "Employment Details",
        fields: [
          {
            id: "jobTitle",
            label: "Job Title",
            type: "text",
            required: true,
            placeholder: "Software Engineer"
          },
          {
            id: "department",
            label: "Department",
            type: "select",
            required: true,
            options: USER_DEPARTMENTS.map(dept => ({
              label: dept.label,
              value: dept.value
            }))
          },
          {
            id: "lifecycleStage",
            label: "Role",
            type: "select",
            required: true,
            options: USER_LIFECYCLE_STAGES.map(role => ({
              label: role,
              value: role
            }))
          },
          {
            id: "leadStatus",
            label: "Status",
            type: "select",
            required: true,
            options: USER_LEAD_STATUSES.map(status => ({
              label: status,
              value: status
            }))
          },
          {
            id: "location",
            label: "Location",
            type: "select",
            options: USER_LOCATIONS.map(location => ({
              label: location.label,
              value: location.value
            }))
          },
          {
            id: "contactOwner",
            label: "Manager/Contact Owner",
            type: "select",
            required: true,
            options: USER_CONTACT_OWNERS.map(owner => ({
              label: owner,
              value: owner
            }))
          }
        ]
      }
    ]
  }

  // Dynamic view configuration for user viewing (currently unused but kept for future use)
  /* const userViewConfig: DynamicViewConfig = {
    title: "User Details",
    header: {
      avatar: {
        field: "avatarUrl",
        fallbackFields: ["firstName", "lastName"]
      },
      title: {
        fields: ["firstName", "lastName"]
      },
      badge: {
        field: "leadStatus",
        variants: {
          "Active": { variant: "default", label: "Active" },
          "Inactive": { variant: "secondary", label: "Inactive" },
          "Pending": { variant: "outline", label: "Pending" },
          "On Leave": { variant: "secondary", label: "On Leave" },
          "Terminated": { variant: "destructive", label: "Terminated" }
        }
      }
    },
    sections: [
      {
        id: "contact",
        title: "Contact Information",
        fields: [
          {
            id: "email",
            label: "Email",
            renderer: "email"
          },
          {
            id: "phoneNumber",
            label: "Phone",
            renderer: "phone"
          }
        ]
      },
      {
        id: "employment",
        title: "Employment Details",
        fields: [
          {
            id: "jobTitle",
            label: "Job Title",
            renderer: "text"
          },
          {
            id: "department",
            label: "Department",
            renderer: "badge",
            variant: "outline"
          },
          {
            id: "lifecycleStage",
            label: "Role",
            renderer: "badge",
            variant: "default"
          },
          {
            id: "location",
            label: "Location",
            renderer: "text"
          },
          {
            id: "contactOwner",
            label: "Manager",
            renderer: "text"
          },
          {
            id: "lastLogin",
            label: "Last Login",
            renderer: "date"
          },
          {
            id: "createdAt",
            label: "Created",
            renderer: "date"
          }
        ]
      }
    ]
  } */

  // Note: Toolbar configuration is now automatically generated from dynamic fields
  // This provides better consistency and reduces code duplication

  return (
    <div className="space-y-6 p-6" data-test-id="user-management_page">
      <div className="flex items-center justify-between" data-test-id="user-management_header">
        <div data-test-id="user-management_title-section">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900" data-test-id="user-management_title">User Management</h1>
          <p className="text-gray-600" data-test-id="user-management_description">
            Manage user accounts, roles, and permissions
          </p>
        </div>
      </div>

      <DynamicTableView
        dynamicConfig={userTableConfig}
        data={users}
        config={{
          searchColumn: "firstName",
          searchPlaceholder: "Search users...",
          pageSize: 10,
          enableRowSelection: true,
          enableColumnVisibility: true,
          showToolbar: true,
          showPagination: true,
        }}
        toolbar={{
          enableExport: true,
          onExport: (format: "csv" | "excel" | "pdf") => {
            console.log(`Exporting users as ${format}`)
            // TODO: Implement export functionality
          },
          extraActions: (
            <Button
              className="bg-blue-600 hover:bg-blue-700"
              onClick={handleAddUser}
              data-test-id="user-management_add-button"
            >
              <Plus className="mr-2 h-4 w-4" data-test-id="user-management_add-icon" />
              Add User
            </Button>
          ),
        }}
        onRowClick={handleViewUser}
        onDeleteRow={handleDeleteUser}
        deleteConfirmTitle="Delete User"
        deleteConfirmMessage="Are you sure you want to delete this user? This action cannot be undone and will remove all associated data."
        data-test-id="user-management_table"
        testIdPrefix="user-management"
      />

      {/* Single CrudCreateDrawer for both create and view/edit */}
      {crudDrawerOpen && (() => {
        console.log("CrudCreateDrawer is open", {
          mode: selectedUser ? 'view/edit' : 'create',
          user: selectedUser?.firstName
        })
        return null
      })()}
      <CrudCreateDrawer
        isOpen={crudDrawerOpen}
        onClose={() => {
          console.log("CrudCreateDrawer onClose called")
          setCrudDrawerOpen(false)
          setSelectedUser(null)
        }}
        dynamicConfig={userFormConfig}
        dynamicData={selectedUser || undefined}
        onDynamicSubmit={handleCreateUser}
        onDynamicEdit={handleCreateUser}
        data-test-id="user-management_form-drawer"
      />
    </div>
  )
}