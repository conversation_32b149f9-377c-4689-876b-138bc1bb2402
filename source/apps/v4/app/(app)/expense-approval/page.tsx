"use client"

/**
 * Expense Approval Page - Strictly following Intent Layout v1 Guidelines
 *
 * This page demonstrates approval workflow management using table-view for structured data display:
 * - Uses DynamicTableView for pending expense requests (>3 attributes, structured data)
 * - Uses crud-drawers for approve/reject actions (<10 fields, low-medium complexity)
 * - Maintains visual consistency with expense-management page styling
 * - Follows intent-layout component selection rules without modification
 * - Implements proper data-test-id attributes with hyphen pattern
 */

import * as React from "react"
import { CheckCircle, XCircle, Eye, Clock, DollarSign, Calendar, Building2, FileText } from "lucide-react"

import { Button } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"

import { DynamicTableView, TableActionsDropdown, type DynamicTableConfig } from "@/components/table-view"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Crud<PERSON>iewDrawer, type DynamicFormConfig, type DynamicViewConfig } from "@/components/crud-drawers"
import { ExpenseData, ExpenseFormData, EXPENSE_CATEGORIES, EXPENSE_STATUSES, EXPENSE_WORKSPACES, EXPENSE_APPROVERS, EXPENSE_LIFECYCLE_STAGES, EXPENSE_LEAD_STATUSES, EXPENSE_CONTACT_OWNERS } from "@/types/expense"

// Mock data for pending expense approvals - following ExpenseData structure
const mockPendingExpenses: ExpenseData[] = [
  {
    id: "1",
    firstName: "Sarah",
    lastName: "Johnson",
    email: "<EMAIL>",
    phoneNumber: "$1,250.00", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Travel", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Conference Travel", // Maps to description
    merchant: "Delta Airlines",
    amount: 1250.00,
    date: "2024-01-22",
    category: "Travel",
    workspace: "Marketing Team",
    tag: "Industry Conference",
    description: "Flight tickets for industry conference in San Francisco",
    receiptUrl: "/receipts/receipt-delta-001.pdf",
    createdAt: "2024-01-22T09:00:00Z",
    updatedAt: "2024-01-22T09:00:00Z",
    notes: ["Awaiting manager approval", "Conference pre-approved"]
  },
  {
    id: "2",
    firstName: "Michael",
    lastName: "Chen",
    email: "<EMAIL>",
    phoneNumber: "$156.78", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Meals & Entertainment", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Client Dinner", // Maps to description
    merchant: "The Capital Grille",
    amount: 156.78,
    date: "2024-01-21",
    category: "Meals & Entertainment",
    workspace: "Sales Department",
    tag: "Client Relations",
    description: "Business dinner with potential enterprise client",
    receiptUrl: "/receipts/receipt-grille-002.pdf",
    createdAt: "2024-01-21T18:30:00Z",
    updatedAt: "2024-01-21T18:30:00Z",
    notes: ["High-value client meeting", "Sales team priority"]
  },
  {
    id: "3",
    firstName: "Emily",
    lastName: "Rodriguez",
    email: "<EMAIL>",
    phoneNumber: "$892.50", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Software & Subscriptions", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Design Software License", // Maps to description
    merchant: "Adobe Systems",
    amount: 892.50,
    date: "2024-01-20",
    category: "Software & Subscriptions",
    workspace: "Marketing Team",
    tag: "Design Tools",
    description: "Annual Adobe Creative Cloud license for design team",
    receiptUrl: "/receipts/receipt-adobe-003.pdf",
    createdAt: "2024-01-20T14:15:00Z",
    updatedAt: "2024-01-20T14:15:00Z",
    notes: ["Required for Q1 campaigns", "Budget approved"]
  },
  {
    id: "4",
    firstName: "David",
    lastName: "Park",
    email: "<EMAIL>",
    phoneNumber: "$67.35", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Transportation", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Airport Transfer", // Maps to description
    merchant: "Premium Taxi Service",
    amount: 67.35,
    date: "2024-01-19",
    category: "Transportation",
    workspace: "Engineering Team",
    tag: "Business Travel",
    description: "Airport taxi for client visit return trip",
    receiptUrl: "/receipts/receipt-taxi-004.pdf",
    createdAt: "2024-01-19T22:45:00Z",
    updatedAt: "2024-01-19T22:45:00Z",
    notes: ["Related to client visit", "Standard rate confirmed"]
  },
  {
    id: "5",
    firstName: "Lisa",
    lastName: "Thompson",
    email: "<EMAIL>",
    phoneNumber: "$234.89", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Office Supplies", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Team Equipment", // Maps to description
    merchant: "Best Buy Business",
    amount: 234.89,
    date: "2024-01-18",
    category: "Office Supplies",
    workspace: "HR Department",
    tag: "Office Setup",
    description: "Wireless keyboards and mice for new hires",
    receiptUrl: "/receipts/receipt-bestbuy-005.pdf",
    createdAt: "2024-01-18T11:20:00Z",
    updatedAt: "2024-01-18T11:20:00Z",
    notes: ["New hire onboarding", "Standard equipment request"]
  },
  {
    id: "6",
    firstName: "Robert",
    lastName: "Kim",
    email: "<EMAIL>",
    phoneNumber: "$445.60", // Maps to amount
    contactOwner: "<EMAIL>",
    lifecycleStage: "Conference & Training", // Maps to category
    leadStatus: "Pending Approval", // Maps to status
    jobTitle: "Training Workshop", // Maps to description
    merchant: "TechEd Solutions",
    amount: 445.60,
    date: "2024-01-17",
    category: "Conference & Training",
    workspace: "Engineering Team",
    tag: "Professional Development",
    description: "Advanced React workshop for development team",
    receiptUrl: "/receipts/receipt-teched-006.pdf",
    createdAt: "2024-01-17T13:10:00Z",
    updatedAt: "2024-01-17T13:10:00Z",
    notes: ["Team skill development", "Training budget allocation"]
  }
]

export default function ExpenseApprovalPage() {
  const [expenses, setExpenses] = React.useState<ExpenseData[]>(mockPendingExpenses)
  const [crudDrawerOpen, setCrudDrawerOpen] = React.useState(false)
  const [selectedExpense, setSelectedExpense] = React.useState<ExpenseData | null>(null)
  const [approvalMode, setApprovalMode] = React.useState<'approve' | 'reject' | 'view'>('view')

  const handleApprovalAction = (expense: ExpenseData, action: 'approve' | 'reject') => {
    const updatedExpense: ExpenseData = {
      ...expense,
      leadStatus: action === 'approve' ? 'Approved' : 'Rejected',
      updatedAt: new Date().toISOString(),
      approvedAt: action === 'approve' ? new Date().toISOString() : undefined,
      rejectedAt: action === 'reject' ? new Date().toISOString() : undefined,
      notes: [
        ...expense.notes || [],
        `${action === 'approve' ? 'Approved' : 'Rejected'} by manager on ${new Date().toLocaleDateString()}`
      ]
    }

    // Update the expense in state and remove from pending list
    setExpenses(prev => prev.filter(e => e.id !== expense.id))
    setCrudDrawerOpen(false)
    setSelectedExpense(null)

    console.log(`Expense ${action}d:`, updatedExpense.merchant, updatedExpense.amount)
  }

  const handleApprovalSubmit = (approvalData: Record<string, any>) => {
    if (selectedExpense && approvalMode !== 'view') {
      const action = approvalMode
      const updatedExpense: ExpenseData = {
        ...selectedExpense,
        leadStatus: action === 'approve' ? 'Approved' : 'Rejected',
        updatedAt: new Date().toISOString(),
        approvedAt: action === 'approve' ? new Date().toISOString() : undefined,
        rejectedAt: action === 'reject' ? new Date().toISOString() : undefined,
        notes: [
          ...selectedExpense.notes || [],
          approvalData.comments ? `Manager note: ${approvalData.comments}` : '',
          `${action === 'approve' ? 'Approved' : 'Rejected'} by manager on ${new Date().toLocaleDateString()}`
        ].filter(Boolean)
      }

      setExpenses(prev => prev.filter(e => e.id !== selectedExpense.id))
      setCrudDrawerOpen(false)
      setSelectedExpense(null)
      setApprovalMode('view')
    }
  }

  const handleViewExpense = (expense: ExpenseData) => {
    console.log("handleViewExpense called - opening expense details", expense.merchant)
    setSelectedExpense(expense)
    setApprovalMode('view')
    setCrudDrawerOpen(true)
  }

  const handleApproveExpense = (expense: ExpenseData) => {
    console.log("handleApproveExpense called - opening approval form", expense.merchant)
    setSelectedExpense(expense)
    setApprovalMode('approve')
    setCrudDrawerOpen(true)
  }

  const handleRejectExpense = (expense: ExpenseData) => {
    console.log("handleRejectExpense called - opening rejection form", expense.merchant)
    setSelectedExpense(expense)
    setApprovalMode('reject')
    setCrudDrawerOpen(true)
  }

  // Dynamic table configuration for expense approval - following table-view guidelines for >3 attributes
  const approvalTableConfig: DynamicTableConfig<ExpenseData> = {
    fields: [
      {
        id: "avatarUrl",
        label: "Receipt",
        type: "avatar",
        size: "default",
        fallbackField: "merchant",
        showName: false,
        sortable: false,
        width: 60,
      },
      {
        id: "firstName",
        label: "Employee",
        type: "custom",
        cellRenderer: ({ row }) => (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={`https://github.com/${row.email.split('@')[0]}.png`} />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                {row.firstName.charAt(0)}{row.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="font-medium text-gray-900">{row.firstName} {row.lastName}</div>
              <div className="text-sm text-gray-600">{row.email}</div>
            </div>
          </div>
        ),
        sortable: true,
        filterable: true,
      },
      {
        id: "merchant",
        label: "Merchant",
        type: "text",
        required: true,
        sortable: true,
        filterable: true,
      },
      {
        id: "description",
        label: "Description",
        type: "text",
        sortable: true,
        filterable: true,
      },
      {
        id: "amount",
        label: "Amount",
        type: "number",
        format: "currency",
        currency: "USD",
        precision: 2,
        sortable: true,
        filterable: false,
      },
      {
        id: "category",
        label: "Category",
        type: "select",
        options: EXPENSE_LIFECYCLE_STAGES.map(category => ({
          label: category,
          value: category,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "workspace",
        label: "Department",
        type: "select",
        options: EXPENSE_WORKSPACES.map(workspace => ({
          label: workspace.label,
          value: workspace.label,
          icon: Building2,
        })),
        sortable: true,
        filterable: true,
      },
      {
        id: "date",
        label: "Date",
        type: "date",
        format: "PPp",
        sortable: true,
        filterable: true,
      },
      {
        id: "createdAt",
        label: "Submitted",
        type: "date",
        format: "PPp",
        sortable: true,
        filterable: false,
      },
      {
        id: "actions",
        label: "Actions",
        type: "custom",
        cellRenderer: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleViewExpense(row as ExpenseData)}
              className="h-8 px-2"
              data-test-id="expense-approval_view-button"
            >
              <Eye className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              onClick={() => handleApproveExpense(row as ExpenseData)}
              className="h-8 px-2 bg-blue-600 hover:bg-blue-700 text-white"
              data-test-id="expense-approval_approve-button"
            >
              <CheckCircle className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRejectExpense(row as ExpenseData)}
              className="h-8 px-2 border-blue-300 text-blue-600 hover:bg-blue-50"
              data-test-id="expense-approval_reject-button"
            >
              <XCircle className="h-3 w-3" />
            </Button>
          </div>
        ),
        sortable: false,
        filterable: false,
        hideable: false,
        width: 150,
      },
    ],
    defaults: {
      sortable: true,
      filterable: true,
      hideable: true,
      defaultVisible: true,
    },
  }

  // Dynamic form configuration for approval actions - following crud-drawer guidelines for <10 fields
  const approvalFormConfig: DynamicFormConfig = {
    title: approvalMode === 'approve' ? "Approve Expense" : "Reject Expense",
    description: approvalMode === 'approve'
      ? "Review and approve this expense request"
      : "Provide reason for rejecting this expense request",
    submitLabel: approvalMode === 'approve' ? "Approve Expense" : "Reject Expense",
    cancelLabel: "Cancel",
    sections: [
      {
        id: "approval",
        title: "Approval Details",
        fields: [
          {
            id: "comments",
            label: approvalMode === 'approve' ? "Approval Comments" : "Rejection Reason",
            type: "textarea",
            required: approvalMode === 'reject',
            placeholder: approvalMode === 'approve'
              ? "Optional comments about the approval..."
              : "Please provide a reason for rejection...",
            validation: {
              maxLength: 500
            }
          },
          {
            id: "notifyEmployee",
            label: "Notify Employee",
            type: "checkbox",
            defaultValue: true,
            helpText: "Send email notification to employee about decision"
          }
        ]
      }
    ]
  }

  // Dynamic view configuration for expense details - following crud-drawer view pattern
  const expenseViewConfig: DynamicViewConfig = {
    title: "Expense Request Details",
    header: {
      title: {
        fields: ["merchant", "description"]
      },
      badge: {
        field: "leadStatus",
        variants: {
          "Pending Approval": { variant: "outline", label: "Pending Approval" },
          "Approved": { variant: "default", label: "Approved" },
          "Rejected": { variant: "destructive", label: "Rejected" }
        }
      }
    },
    sections: [
      {
        id: "employee",
        title: "Employee Information",
        fields: [
          {
            id: "firstName",
            label: "Employee Name",
            renderer: "custom",
            customRenderer: (value, data) => `${data.firstName} ${data.lastName}`
          },
          {
            id: "email",
            label: "Email",
            renderer: "email"
          },
          {
            id: "workspace",
            label: "Department",
            renderer: "badge",
            variant: "outline"
          }
        ]
      },
      {
        id: "expense",
        title: "Expense Information",
        fields: [
          {
            id: "amount",
            label: "Amount",
            renderer: "custom",
            customRenderer: (value) => `$${value?.toLocaleString()}`
          },
          {
            id: "category",
            label: "Category",
            renderer: "badge",
            variant: "outline"
          },
          {
            id: "date",
            label: "Expense Date",
            renderer: "date"
          },
          {
            id: "description",
            label: "Description",
            renderer: "text"
          },
          {
            id: "tag",
            label: "Tag",
            renderer: "text"
          }
        ]
      },
      {
        id: "submission",
        title: "Submission Details",
        fields: [
          {
            id: "createdAt",
            label: "Submitted",
            renderer: "date"
          },
          {
            id: "contactOwner",
            label: "Approver",
            renderer: "text"
          },
          {
            id: "receiptUrl",
            label: "Receipt",
            renderer: "url"
          }
        ]
      }
    ]
  }

  return (
    <div className="flex flex-col min-h-0 flex-1" data-test-id="expense-approval_page">
      {/* Fixed Header Section */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 p-6" data-test-id="expense-approval_header">
        <div className="flex items-center justify-between" data-test-id="expense-approval_header-content">
          <div data-test-id="expense-approval_title-section">
            <h1 className="text-3xl font-bold tracking-tight text-gray-900" data-test-id="expense-approval_title">
              Expense Approval
            </h1>
            <p className="text-gray-600" data-test-id="expense-approval_description">
              Review and approve pending expense requests from your team
            </p>
          </div>
          <div className="flex items-center space-x-4" data-test-id="expense-approval_stats">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-600">
                {expenses.length} pending requests
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-600">
                ${expenses.reduce((sum, exp) => sum + exp.amount, 0).toLocaleString()} total
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Table Container with Proper Height Calculation */}
      <div className="flex-1 min-h-0 p-6" data-test-id="expense-approval_table-container">
        <div className="h-full flex flex-col">
          <DynamicTableView
            dynamicConfig={approvalTableConfig}
            data={expenses}
            config={{
              searchColumn: "merchant",
              searchPlaceholder: "Search expenses by merchant, employee, or description...",
              pageSize: 10,
              enableRowSelection: true,
              enableColumnVisibility: true,
              showToolbar: true,
              showPagination: true,
            }}
            toolbar={{
              enableExport: true,
              onExport: (format: "csv" | "excel" | "pdf") => {
                console.log(`Exporting pending approvals as ${format}`)
                // TODO: Implement export functionality
              },
              extraActions: (
                <div className="flex items-center space-x-3">
                  <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
                    <Clock className="mr-1 h-3 w-3" />
                    {expenses.length} Pending
                  </Badge>
                  <Button
                    variant="outline"
                    className="border-blue-300 text-blue-600 hover:bg-blue-50"
                    onClick={() => {
                      // Bulk approve functionality
                      console.log("Bulk approval feature")
                    }}
                    data-test-id="expense-approval_bulk-approve-button"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Bulk Approve
                  </Button>
                </div>
              ),
            }}
            onRowClick={handleViewExpense}
            className="h-full"
            data-test-id="expense-approval_table"
            testIdPrefix="expense-approval"
          />
        </div>
      </div>

      {/* Single CrudCreateDrawer for approval actions - Following Intent Layout Guidelines */}
      {crudDrawerOpen && (() => {
        console.log("CrudCreateDrawer is open", {
          mode: approvalMode,
          expense: selectedExpense?.merchant
        })
        return null
      })()}

      {approvalMode === 'view' ? (
        <CrudViewDrawer
          isOpen={crudDrawerOpen}
          onClose={() => {
            console.log("CrudViewDrawer onClose called")
            setCrudDrawerOpen(false)
            setSelectedExpense(null)
            setApprovalMode('view')
          }}
          dynamicConfig={expenseViewConfig}
          dynamicData={selectedExpense || undefined}
          data-test-id="expense-approval_view-drawer"
          actions={
            <div className="flex space-x-3">
              <Button
                onClick={() => {
                  if (selectedExpense) {
                    setApprovalMode('approve')
                  }
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white"
                data-test-id="expense-approval_view-approve-button"
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  if (selectedExpense) {
                    setApprovalMode('reject')
                  }
                }}
                className="border-blue-300 text-blue-600 hover:bg-blue-50"
                data-test-id="expense-approval_view-reject-button"
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
            </div>
          }
        />
      ) : (
        <CrudCreateDrawer
          isOpen={crudDrawerOpen}
          onClose={() => {
            console.log("CrudCreateDrawer onClose called")
            setCrudDrawerOpen(false)
            setSelectedExpense(null)
            setApprovalMode('view')
          }}
          dynamicConfig={approvalFormConfig}
          dynamicData={undefined}
          onDynamicSubmit={handleApprovalSubmit}
          data-test-id="expense-approval_form-drawer"
        />
      )}
    </div>
  )
}