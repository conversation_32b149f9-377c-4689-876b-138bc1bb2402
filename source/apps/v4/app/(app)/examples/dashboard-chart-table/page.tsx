"use client"

import { useState } from "react"
import { ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown, Eye, Edit, Trash2 } from "lucide-react"

import { But<PERSON> } from "@/registry/new-york-v4/ui/button"
import { Badge } from "@/registry/new-york-v4/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import { Checkbox } from "@/registry/new-york-v4/ui/checkbox"
import { DashboardChartTable } from "@/blocks/dashboard-chart-table"

import tableData from "./data.json"

export interface UserData {
  id: string
  name: string
  email: string
  status: "active" | "inactive" | "pending"
  role: string
  department: string
  lastActivity: string
  revenue: number
  orders: number
  country: string
}

// Convert JSON data to typed data
const data: UserData[] = tableData as UserData[]

// Filter options for the table toolbar
const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Pending", value: "pending" },
]

const departmentOptions = [
  { label: "Engineering", value: "Engineering" },
  { label: "Sales", value: "Sales" },
  { label: "Design", value: "Design" },
  { label: "Marketing", value: "Marketing" },
  { label: "Analytics", value: "Analytics" },
  { label: "Operations", value: "Operations" },
  { label: "Support", value: "Support" },
]

export default function DashboardChartTableExample() {
  const [selectedRows, setSelectedRows] = useState<UserData[]>([])

  // Handle row selection
  const handleRowClick = (row: UserData) => {
    console.log("Row clicked:", row)
  }

  // Handle row deletion
  const handleDeleteRow = (row: UserData) => {
    console.log("Delete row:", row)
    // Implement delete logic here
  }

  // Status badge renderer
  const StatusBadge = ({ status }: { status: UserData["status"] }) => {
    const variants = {
      active: "default",
      inactive: "secondary",
      pending: "outline",
    } as const

    const colors = {
      active: "bg-blue-100 text-blue-700 border-blue-200",
      inactive: "bg-gray-100 text-gray-800 border-gray-200",
      pending: "bg-blue-50 text-blue-600 border-blue-200",
    }

    return (
      <Badge
        variant={variants[status]}
        className={colors[status]}
        data-test-id={`dashboard-chart-table_status-badge-${status}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // Table column definitions
  const columns: ColumnDef<UserData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          data-test-id="dashboard-chart-table_select-all-checkbox"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          data-test-id={`dashboard-chart-table_select-checkbox-${row.original.id}`}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium text-left justify-start"
          data-test-id="dashboard-chart-table_name-sort-button"
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium" data-test-id={`dashboard-chart-table_name-${row.original.id}`}>
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => (
        <div className="text-muted-foreground" data-test-id={`dashboard-chart-table_email-${row.original.id}`}>
          {row.getValue("email")}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.getValue("status")} />,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => (
        <div data-test-id={`dashboard-chart-table_role-${row.original.id}`}>
          {row.getValue("role")}
        </div>
      ),
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => (
        <div data-test-id={`dashboard-chart-table_department-${row.original.id}`}>
          {row.getValue("department")}
        </div>
      ),
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: "revenue",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full"
          data-test-id="dashboard-chart-table_revenue-sort-button"
        >
          Revenue
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("revenue"))
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)

        return (
          <div className="text-right font-medium" data-test-id={`dashboard-chart-table_revenue-${row.original.id}`}>
            {formatted}
          </div>
        )
      },
    },
    {
      accessorKey: "orders",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-8 p-0 font-medium justify-end text-right w-full"
          data-test-id="dashboard-chart-table_orders-sort-button"
        >
          Orders
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-right" data-test-id={`dashboard-chart-table_orders-${row.original.id}`}>
          {row.getValue("orders")}
        </div>
      ),
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }) => (
        <div data-test-id={`dashboard-chart-table_country-${row.original.id}`}>
          {row.getValue("country")}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const user = row.original

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                data-test-id={`dashboard-chart-table_actions-${user.id}`}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-test-id={`dashboard-chart-table_actions-menu-${user.id}`}>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(user.id)}
                data-test-id={`dashboard-chart-table_copy-id-${user.id}`}
              >
                Copy user ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem data-test-id={`dashboard-chart-table_view-${user.id}`}>
                <Eye className="mr-2 h-4 w-4" />
                View details
              </DropdownMenuItem>
              <DropdownMenuItem data-test-id={`dashboard-chart-table_edit-${user.id}`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit user
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteRow(user)}
                data-test-id={`dashboard-chart-table_delete-${user.id}`}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete user
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
      enableSorting: false,
      enableHiding: false,
    },
  ]

  return (
    <>
      <div className="md:hidden" data-test-id="dashboard-chart-table_mobile">
        <img
          src="/examples/dashboard-light.png"
          width={1280}
          height={998}
          alt="Dashboard Chart Table"
          className="block dark:hidden"
          data-test-id="dashboard-chart-table_light-image"
        />
        <img
          src="/examples/dashboard-dark.png"
          width={1280}
          height={998}
          alt="Dashboard Chart Table"
          className="hidden dark:block"
          data-test-id="dashboard-chart-table_dark-image"
        />
      </div>
      <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex" data-test-id="dashboard-chart-table-page">
        <div className="flex items-center justify-between space-y-2" data-test-id="dashboard-chart-table-page_header">
          <div data-test-id="dashboard-chart-table-page_title-section">
            <h2 className="text-2xl font-bold tracking-tight" data-test-id="dashboard-chart-table-page_title">Dashboard with Charts and Table</h2>
            <p className="text-muted-foreground" data-test-id="dashboard-chart-table-page_description">
              A comprehensive dashboard layout demonstrating the integration of summary cards, charts, and data tables
              following the intent-layout-v1.md guidelines.
            </p>
          </div>
        </div>

        <DashboardChartTable
          summaryData={{
            totalRevenue: 45231.89,
            revenueChange: 20.1,
            totalUsers: 2350,
            usersChange: 15.2,
            activeUsers: 573,
            activeChange: -2.4,
            conversionRate: 3.2,
            conversionChange: 8.1,
          }}
          tableProps={{
            columns,
            data,
            config: {
              searchColumn: "name",
              searchPlaceholder: "Search by name...",
              pageSize: 10,
              enableRowSelection: true,
              enableColumnVisibility: true,
              showToolbar: true,
              showPagination: true,
            },
            toolbar: {
              filters: [
                {
                  column: "status",
                  title: "Status",
                  options: statusOptions,
                },
                {
                  column: "department",
                  title: "Department",
                  options: departmentOptions,
                },
              ],
              enableColumnVisibility: true,
              enableExport: true,
            },
            onRowClick: handleRowClick,
            onDeleteRow: handleDeleteRow,
            deleteConfirmTitle: "Delete User",
            deleteConfirmMessage: "Are you sure you want to delete this user? This action cannot be undone.",
          }}
          showCharts={true}
          chartVariant="default"
          sectionGap="lg"
          className="w-full"
          data-test-id="dashboard-chart-table-component"
        />
      </div>
    </>
  )
}