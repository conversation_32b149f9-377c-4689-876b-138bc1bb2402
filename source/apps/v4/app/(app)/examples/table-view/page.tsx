import { Metadata } from "next"

import { Separator } from "@/registry/new-york-v4/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/registry/new-york-v4/ui/tabs"

import { TransactionsTable } from "@/components/table-view/examples/transactions-table"
import { AnalyticsTable } from "@/components/table-view/examples/analytics-table"
import { DynamicFieldsTable, SimpleProductTable } from "@/components/table-view/examples/dynamic-fields-table"

export const metadata: Metadata = {
  title: "Table View Examples",
  description: "Dynamic field configuration and static column approaches for adaptive table components.",
}

export default function TableViewPage() {
  return (
    <>
      <div className="md:hidden" data-test-id="table-view-examples_mobile">
        <img
          src="/examples/tasks-light.png"
          width={1280}
          height={998}
          alt="Table View"
          className="block dark:hidden"
          data-test-id="table-view-examples_light-image"
        />
        <img
          src="/examples/tasks-dark.png"
          width={1280}
          height={998}
          alt="Table View"
          className="hidden dark:block"
          data-test-id="table-view-examples_dark-image"
        />
      </div>
      <div className="hidden h-full flex-1 flex-col space-y-8 p-8 md:flex" data-test-id="table-view-examples_page">
        <div className="flex items-center justify-between space-y-2" data-test-id="table-view-examples_header">
          <div data-test-id="table-view-examples_title-section">
            <h2 className="text-2xl font-bold tracking-tight" data-test-id="table-view-examples_title">Table View Examples</h2>
            <p className="text-muted-foreground" data-test-id="table-view-examples_description">
              Dynamic field configuration and traditional static columns for adaptive table components with advanced functionality.
            </p>
          </div>
        </div>
        <Separator data-test-id="table-view-examples_separator" />

        <Tabs defaultValue="dynamic-fields" className="space-y-4" data-test-id="table-view-examples_tabs">
          <TabsList data-test-id="table-view-examples_tabs-list">
            <TabsTrigger value="dynamic-fields" data-test-id="table-view-examples_dynamic-fields-tab">Dynamic Fields</TabsTrigger>
            <TabsTrigger value="products" data-test-id="table-view-examples_products-tab">Products</TabsTrigger>
            <TabsTrigger value="transactions" data-test-id="table-view-examples_transactions-tab">Transactions (Static)</TabsTrigger>
            <TabsTrigger value="analytics" data-test-id="table-view-examples_analytics-tab">Analytics (Static)</TabsTrigger>
          </TabsList>

          <TabsContent value="transactions" className="space-y-4" data-test-id="table-view-examples_transactions-content">
            <div data-test-id="table-view-examples_transactions-header">
              <h3 className="text-lg font-medium" data-test-id="table-view-examples_transactions-title">Financial Transactions (Static Columns)</h3>
              <p className="text-sm text-muted-foreground" data-test-id="table-view-examples_transactions-description">
                A table view with traditional static column definitions. Good for custom rendering
                and complex interactions that need full control.
              </p>
            </div>
            <TransactionsTable />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4" data-test-id="table-view-examples_analytics-content">
            <div data-test-id="table-view-examples_analytics-header">
              <h3 className="text-lg font-medium" data-test-id="table-view-examples_analytics-title">Session Analytics (Static Columns)</h3>
              <p className="text-sm text-muted-foreground" data-test-id="table-view-examples_analytics-description">
                A compact table view with static column definitions. Shows the traditional approach
                for comparison with dynamic field configuration.
              </p>
            </div>
            <AnalyticsTable />
          </TabsContent>

          <TabsContent value="dynamic-fields" className="space-y-4" data-test-id="table-view-examples_dynamic-fields-content">
            <div data-test-id="table-view-examples_dynamic-fields-header">
              <h3 className="text-lg font-medium" data-test-id="table-view-examples_dynamic-fields-title">Dynamic Fields - Employee Directory</h3>
              <p className="text-sm text-muted-foreground" data-test-id="table-view-examples_dynamic-fields-description">
                ⭐ <strong>Recommended Approach:</strong> Dynamic field configuration with automatic column generation,
                type-specific renderers, smart filtering, and enhanced functionality. Features avatar fields,
                badges, ratings, nested data access, and more.
              </p>
            </div>
            <DynamicFieldsTable />
          </TabsContent>

          <TabsContent value="products" className="space-y-4" data-test-id="table-view-examples_products-content">
            <div data-test-id="table-view-examples_products-header">
              <h3 className="text-lg font-medium" data-test-id="table-view-examples_products-title">Dynamic Fields - Product Catalog</h3>
              <p className="text-sm text-muted-foreground" data-test-id="table-view-examples_products-description">
                ⭐ <strong>Quick Setup:</strong> A simpler example showing basic dynamic field types and how to rapidly build
                adaptive tables for any data structure with minimal code.
              </p>
            </div>
            <SimpleProductTable />
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}