import { Metadata } from "next"

import { DynamicFieldsTable, SimpleProductTable } from "@/components/table-view/examples"

export const metadata: Metadata = {
  title: "Dynamic Table View",
  description: "Table view with dynamic field configurations",
}

export default function DynamicTableViewPage() {
  return (
    <div className="space-y-8" data-test-id="dynamic-table-view_page">
      <div data-test-id="dynamic-table-view_header">
        <h1 className="text-3xl font-bold tracking-tight" data-test-id="dynamic-table-view_title">Dynamic Table View</h1>
        <p className="text-muted-foreground mt-2" data-test-id="dynamic-table-view_description">
          Examples demonstrating dynamic field configurations for flexible table schemas
        </p>
      </div>

      <div className="space-y-8" data-test-id="dynamic-table-view_examples">
        <section data-test-id="dynamic-table-view_employee-section">
          <h2 className="text-xl font-semibold mb-4" data-test-id="dynamic-table-view_employee-title">Employee Directory Example</h2>
          <p className="text-muted-foreground mb-6" data-test-id="dynamic-table-view_employee-description">
            A comprehensive example showing various field types including avatars, ratings,
            nested data access, and complex filtering.
          </p>
          <DynamicFieldsTable />
        </section>

        <section data-test-id="dynamic-table-view_product-section">
          <h2 className="text-xl font-semibold mb-4" data-test-id="dynamic-table-view_product-title">Simple Product Catalog</h2>
          <p className="text-muted-foreground mb-6" data-test-id="dynamic-table-view_product-description">
            A simpler example demonstrating basic field types and configurations.
          </p>
          <SimpleProductTable />
        </section>
      </div>

      <div className="bg-muted/50 rounded-lg p-6" data-test-id="dynamic-table-view_features">
        <h3 className="font-semibold mb-3" data-test-id="dynamic-table-view_features-title">Dynamic Fields Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm" data-test-id="dynamic-table-view_features-grid">
          <div data-test-id="dynamic-table-view_field-types">
            <h4 className="font-medium mb-2" data-test-id="dynamic-table-view_field-types-title">Field Types Supported:</h4>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Text (with transformations)</li>
              <li>• Number (with formatting)</li>
              <li>• Date (with custom formats)</li>
              <li>• Boolean (switch/checkbox)</li>
              <li>• Select (with icons)</li>
              <li>• Email (clickable links)</li>
              <li>• URL (external links)</li>
              <li>• Badge (multiple variants)</li>
              <li>• Avatar (with fallbacks)</li>
              <li>• Rating (stars/numeric/progress)</li>
              <li>• Progress bars</li>
              <li>• Custom renderers</li>
            </ul>
          </div>
          <div data-test-id="dynamic-table-view_key-features">
            <h4 className="font-medium mb-2" data-test-id="dynamic-table-view_key-features-title">Key Features:</h4>
            <ul className="space-y-1 text-muted-foreground" data-test-id="dynamic-table-view_key-features-list">
              <li>• Type-safe configuration</li>
              <li>• Automatic filtering by field type</li>
              <li>• Nested data access</li>
              <li>• Custom validation rules</li>
              <li>• Column visibility controls</li>
              <li>• Backward compatibility</li>
              <li>• Export functionality</li>
              <li>• Search integration</li>
              <li>• Responsive design</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}