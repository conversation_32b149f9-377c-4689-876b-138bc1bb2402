"use client"

import * as React from "react"
import { BarChart3, Users, DollarSign, TrendingUp } from "lucide-react"

import { But<PERSON> } from "@/registry/new-york-v4/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/registry/new-york-v4/ui/card"

const stats = [
  {
    title: "Total Users",
    value: "2,350",
    description: "Active user accounts",
    icon: Users,
    trend: "+12% from last month",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Revenue",
    value: "$45,231",
    description: "Total revenue this month",
    icon: DollarSign,
    trend: "+8% from last month",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Growth Rate",
    value: "12.5%",
    description: "Monthly growth",
    icon: TrendingUp,
    trend: "+2.1% from last month",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Reports Generated",
    value: "573",
    description: "This month",
    icon: BarChart3,
    trend: "+19% from last month",
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
]

export default function GeneralDashboardPage() {
  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">General Dashboard</h1>
          <p className="text-gray-600">
            Overview of your system performance and metrics
          </p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title} className="border-blue-100 hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <p className="text-xs text-gray-600 mt-1">
                  {stat.description}
                </p>
                <p className="text-xs text-blue-600 mt-2 font-medium">
                  {stat.trend}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 border-blue-100">
          <CardHeader>
            <CardTitle className="text-gray-900">Recent Activity</CardTitle>
            <CardDescription>
              Latest updates from your system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4 p-3 rounded-lg bg-blue-50">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600">
                <Users className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">New user registered</p>
                <p className="text-xs text-gray-600">Alice Brown joined the system</p>
              </div>
              <span className="text-xs text-gray-500">2 min ago</span>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg bg-blue-50">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600">
                <DollarSign className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Payment processed</p>
                <p className="text-xs text-gray-600">$1,245.67 transaction completed</p>
              </div>
              <span className="text-xs text-gray-500">5 min ago</span>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg bg-blue-50">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Report generated</p>
                <p className="text-xs text-gray-600">Monthly analytics report is ready</p>
              </div>
              <span className="text-xs text-gray-500">1 hour ago</span>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3 border-blue-100">
          <CardHeader>
            <CardTitle className="text-gray-900">Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start gap-3 bg-blue-600 hover:bg-blue-700 text-white" asChild>
              <a href="/user-management">
                <Users className="h-5 w-5" />
                <div className="text-left">
                  <p className="text-sm font-medium">Manage Users</p>
                  <p className="text-xs opacity-80">View and edit user accounts</p>
                </div>
              </a>
            </Button>

            <Button className="w-full justify-start gap-3 bg-blue-600 hover:bg-blue-700 text-white" asChild>
              <a href="/expense-management">
                <DollarSign className="h-5 w-5" />
                <div className="text-left">
                  <p className="text-sm font-medium">View Expenses</p>
                  <p className="text-xs opacity-80">Track expense submissions</p>
                </div>
              </a>
            </Button>

            <Button className="w-full justify-start gap-3 bg-blue-600 hover:bg-blue-700 text-white" asChild>
              <a href="/analytics-dashboard">
                <BarChart3 className="h-5 w-5" />
                <div className="text-left">
                  <p className="text-sm font-medium">Analytics Dashboard</p>
                  <p className="text-xs opacity-80">View comprehensive analytics</p>
                </div>
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}