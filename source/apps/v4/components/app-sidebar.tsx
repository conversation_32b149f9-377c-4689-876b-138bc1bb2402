"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { Users, Receipt, LayoutDashboard, Settings, LogOut, CheckCircle, BarChart3 } from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter,
  SidebarSeparator,
} from "@/registry/new-york-v4/ui/sidebar"
import { Button } from "@/registry/new-york-v4/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/registry/new-york-v4/ui/avatar"

interface NavigationItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
}

const navigationItems: NavigationItem[] = [
  {
    title: "Dashboard",
    href: "/general-dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Analytics Dashboard",
    href: "/analytics-dashboard",
    icon: BarChart3,
  },
  {
    title: "Analytics Dashboard V2",
    href: "/analytics-dashboard-v2",
    icon: BarChart3,
  },
  {
    title: "User Management",
    href: "/user-management",
    icon: Users,
  },
  {
    title: "Expense Management",
    href: "/expense-management",
    icon: Receipt,
  },
  {
    title: "Expense Approval",
    href: "/expense-approval",
    icon: CheckCircle,
  },
]

const settingsItems: NavigationItem[] = [
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  return (
    <Sidebar className="border-r border-blue-200 bg-white">
      <SidebarHeader className="border-b border-blue-100 p-4">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
            <span className="text-sm font-semibold text-white">CM</span>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Core Manager</h2>
            <p className="text-xs text-gray-600">Management Suite</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="p-2">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-600 uppercase tracking-wider">
            Main Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const isActive = pathname === item.href
                const Icon = item.icon

                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      className={`
                        h-10 px-3 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                        ${isActive
                          ? "bg-blue-600 text-white font-medium shadow-md hover:bg-blue-700"
                          : "text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:text-blue-700"
                        }
                      `}
                    >
                      <Link href={item.href} className="flex items-center gap-3 w-full">
                        <Icon className={`h-5 w-5 ${isActive ? "text-white" : "text-gray-500"}`} />
                        <span className="flex-1">{item.title}</span>
                        {item.badge && (
                          <span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full font-medium">
                            {item.badge}
                          </span>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator className="my-4 bg-blue-100" />

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-gray-600 uppercase tracking-wider">
            System
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {settingsItems.map((item) => {
                const isActive = pathname === item.href
                const Icon = item.icon

                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      className={`
                        h-10 px-3 rounded-lg transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                        ${isActive
                          ? "bg-blue-600 text-white font-medium shadow-md hover:bg-blue-700"
                          : "text-gray-700 hover:bg-blue-50 hover:text-blue-700 focus:text-blue-700"
                        }
                      `}
                    >
                      <Link href={item.href} className="flex items-center gap-3 w-full">
                        <Icon className={`h-5 w-5 ${isActive ? "text-white" : "text-gray-500"}`} />
                        <span className="flex-1">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-blue-100 p-4">
        <div className="flex items-center gap-3 mb-3">
          <Avatar className="h-8 w-8 ring-2 ring-blue-100">
            <AvatarImage src="/avatars/admin.jpg" alt="Admin User" />
            <AvatarFallback className="bg-blue-100 text-blue-700 text-xs font-medium">
              AU
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">Admin User</p>
            <p className="text-xs text-gray-600 truncate"><EMAIL></p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start gap-2 h-8 text-gray-600 hover:text-red-600 hover:bg-red-50"
        >
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </Button>
      </SidebarFooter>
    </Sidebar>
  )
}