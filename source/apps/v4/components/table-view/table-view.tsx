"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/registry/new-york-v4/ui/table"
import { Skeleton } from "@/registry/new-york-v4/ui/skeleton"
import { Button } from "@/registry/new-york-v4/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/registry/new-york-v4/ui/dialog"

import { TableViewToolbar, TableViewToolbarProps } from "./table-view-toolbar"
import { TableViewPagination } from "./table-view-pagination"

// Context for providing delete functionality to table cells
interface TableViewDeleteContext<TData = any> {
  onDelete: (row: TData) => void
  canDelete: boolean
}

const TableViewDeleteContext = React.createContext<TableViewDeleteContext | null>(null)

export const useTableViewDelete = <TData = any>() => {
  const context = React.useContext(TableViewDeleteContext) as TableViewDeleteContext<TData> | null
  return context
}

export interface TableViewConfig {
  searchColumn?: string
  searchPlaceholder?: string
  pageSize?: number
  enableRowSelection?: boolean
  enableColumnVisibility?: boolean
  showToolbar?: boolean
  showPagination?: boolean
  variant?: "default" | "compact" | "spacious"
}

export interface TableViewProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  config?: TableViewConfig
  toolbar?: Partial<TableViewToolbarProps<TData>>
  loading?: boolean
  error?: string | null
  onRowClick?: (row: TData) => void
  onDeleteRow?: (row: TData) => void
  showDeleteConfirm?: boolean
  deleteConfirmTitle?: string
  deleteConfirmMessage?: string
  className?: string
  /** Test ID for the component */
  'data-test-id'?: string
  /** Test ID prefix for child elements */
  testIdPrefix?: string
}

export function TableView<TData, TValue>({
  columns,
  data,
  config = {},
  toolbar = {},
  loading = false,
  error = null,
  onRowClick,
  onDeleteRow,
  showDeleteConfirm = true,
  deleteConfirmTitle = "Delete Row",
  deleteConfirmMessage = "Are you sure you want to delete this row? This action cannot be undone.",
  className,
  'data-test-id': testId,
  testIdPrefix,
}: TableViewProps<TData, TValue>) {
  const {
    searchColumn,
    pageSize = 25,
    enableRowSelection = true,
    enableColumnVisibility = true,
    showToolbar = true,
    showPagination = true,
    variant = "default",
  } = config

  const [rowSelection, setRowSelection] = React.useState({})
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  )
  const [sorting, setSorting] = React.useState<SortingState>([])

  // Delete confirmation modal state
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false)
  const [rowToDelete, setRowToDelete] = React.useState<TData | null>(null)

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize,
      },
    },
    enableRowSelection,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  })

  // Delete functionality handlers
  const handleDeleteRow = React.useCallback((row: TData) => {
    if (!onDeleteRow) return

    if (showDeleteConfirm) {
      setRowToDelete(row)
      setDeleteModalOpen(true)
    } else {
      onDeleteRow(row)
    }
  }, [onDeleteRow, showDeleteConfirm])

  const handleConfirmDelete = React.useCallback(() => {
    if (rowToDelete && onDeleteRow) {
      onDeleteRow(rowToDelete)
    }
    setDeleteModalOpen(false)
    setRowToDelete(null)
  }, [rowToDelete, onDeleteRow])

  const handleCancelDelete = React.useCallback(() => {
    setDeleteModalOpen(false)
    setRowToDelete(null)
  }, [])

  // Create a context value for delete functionality
  const deleteContext = React.useMemo(() => ({
    onDelete: handleDeleteRow,
    canDelete: !!onDeleteRow
  }), [handleDeleteRow, onDeleteRow])

  // Get row styles based on variant
  const getVariantClasses = () => {
    switch (variant) {
      case "compact":
        return "text-xs"
      case "spacious":
        return "text-sm py-4"
      default:
        return "text-sm"
    }
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center" data-test-id={`${testId || testIdPrefix}_error`}>
        <div className="text-muted-foreground text-sm" data-test-id={`${testId || testIdPrefix}_error-message`}>
          Error loading data: {error}
        </div>
      </div>
    )
  }

  return (
    <TableViewDeleteContext.Provider value={deleteContext}>
      <div className={`flex flex-col gap-4 ${className?.includes('h-full') ? 'h-full' : ''} ${className || ""}`} data-test-id={testId}>
        {showToolbar && (
          <TableViewToolbar
            table={table}
            searchColumn={searchColumn}
            enableColumnVisibility={enableColumnVisibility}
            testIdPrefix={testIdPrefix || testId}
            {...toolbar}
          />
        )}

        <div className={`overflow-hidden rounded-md border ${className?.includes('h-full') ? 'flex-1 flex flex-col' : ''}`} data-test-id={`${testIdPrefix || testId}_table-container`}>
          <div className={className?.includes('h-full') ? 'flex-1 overflow-auto' : ''}>
            <Table data-test-id={`${testIdPrefix || testId}_table`}>
              <TableHeader data-test-id={`${testIdPrefix || testId}_table-header`}>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} data-test-id={`${testIdPrefix || testId}_header-row`}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} colSpan={header.colSpan} data-test-id={`${testIdPrefix || testId}_header-${header.id}`}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody data-test-id={`${testIdPrefix || testId}_table-body`}>
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }, (_, i) => (
                    <TableRow key={`loading-${i}`} data-test-id={`${testIdPrefix || testId}_loading-row-${i}`}>
                      {columns.map((_, j) => (
                        <TableCell key={`loading-cell-${j}`} data-test-id={`${testIdPrefix || testId}_loading-cell-${i}-${j}`}>
                          <Skeleton className="h-4 w-full" data-test-id={`${testIdPrefix || testId}_loading-skeleton-${i}-${j}`} />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, rowIndex) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className={`${getVariantClasses()} ${
                        onRowClick ? "cursor-pointer hover:bg-muted/50" : ""
                      }`}
                      onClick={() => onRowClick?.(row.original)}
                      data-test-id={`${testIdPrefix || testId}_data-row-${rowIndex}`}
                    >
                      {row.getVisibleCells().map((cell, cellIndex) => (
                        <TableCell key={cell.id} data-test-id={`${testIdPrefix || testId}_data-cell-${rowIndex}-${cellIndex}`}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow data-test-id={`${testIdPrefix || testId}_empty-row`}>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                      data-test-id={`${testIdPrefix || testId}_empty-message`}
                    >
                      No results found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {showPagination && (
          <TableViewPagination table={table} testIdPrefix={testIdPrefix || testId} />
        )}

        {/* Delete Confirmation Modal */}
        <Dialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen} data-test-id={`${testIdPrefix || testId}_delete-modal`}>
          <DialogContent data-test-id={`${testIdPrefix || testId}_delete-modal-content`}>
            <DialogHeader data-test-id={`${testIdPrefix || testId}_delete-modal-header`}>
              <DialogTitle data-test-id={`${testIdPrefix || testId}_delete-modal-title`}>{deleteConfirmTitle}</DialogTitle>
              <DialogDescription data-test-id={`${testIdPrefix || testId}_delete-modal-description`}>
                {deleteConfirmMessage}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter data-test-id={`${testIdPrefix || testId}_delete-modal-footer`}>
              <Button
                variant="outline"
                onClick={handleCancelDelete}
                data-test-id={`${testIdPrefix || testId}_delete-cancel-button`}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmDelete}
                className="bg-red-600 hover:bg-red-700"
                data-test-id={`${testIdPrefix || testId}_delete-confirm-button`}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TableViewDeleteContext.Provider>
  )
}