import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"

/**
 * Base field configuration that applies to all field types
 */
export interface BaseFieldConfig {
  /** Unique identifier for the field */
  id: string
  /** Display label for the field */
  label: string
  /** Description or help text for the field */
  description?: string
  /** Whether the field is required */
  required?: boolean
  /** Whether the field can be sorted */
  sortable?: boolean
  /** Whether the field can be filtered */
  filterable?: boolean
  /** Whether the field can be hidden */
  hideable?: boolean
  /** Default visibility state */
  defaultVisible?: boolean
  /** Custom width for the column */
  width?: number | string
  /** Custom CSS classes for the column */
  className?: string
}

/**
 * Text field configuration
 */
export interface TextFieldConfig extends BaseFieldConfig {
  type: "text"
  /** Placeholder text */
  placeholder?: string
  /** Maximum length */
  maxLength?: number
  /** Whether to show as multiline */
  multiline?: boolean
  /** Text transformation */
  transform?: "uppercase" | "lowercase" | "capitalize"
}

/**
 * Number field configuration
 */
export interface NumberFieldConfig extends BaseFieldConfig {
  type: "number"
  /** Minimum value */
  min?: number
  /** Maximum value */
  max?: number
  /** Number of decimal places */
  precision?: number
  /** Number format (currency, percentage, etc.) */
  format?: "currency" | "percentage" | "decimal" | "integer"
  /** Currency code for currency format */
  currency?: string
  /** Custom prefix/suffix */
  prefix?: string
  suffix?: string
}

/**
 * Date field configuration
 */
export interface DateFieldConfig extends BaseFieldConfig {
  type: "date"
  /** Date format for display */
  format?: string
  /** Whether to include time */
  includeTime?: boolean
  /** Minimum date */
  minDate?: Date
  /** Maximum date */
  maxDate?: Date
  /** Default date value */
  defaultValue?: Date
}

/**
 * Boolean field configuration
 */
export interface BooleanFieldConfig extends BaseFieldConfig {
  type: "boolean"
  /** Display as switch or checkbox */
  variant?: "switch" | "checkbox"
  /** Custom labels for true/false states */
  trueLabel?: string
  falseLabel?: string
}

/**
 * Select field configuration
 */
export interface SelectFieldConfig extends BaseFieldConfig {
  type: "select"
  /** Available options */
  options: Array<{
    label: string
    value: string | number
    description?: string
    icon?: React.ComponentType<{ className?: string }>
    disabled?: boolean
  }>
  /** Whether multiple selection is allowed */
  multiple?: boolean
  /** Whether options are searchable */
  searchable?: boolean
  /** Placeholder text */
  placeholder?: string
  /** Whether to allow custom values */
  allowCustom?: boolean
}

/**
 * Email field configuration
 */
export interface EmailFieldConfig extends BaseFieldConfig {
  type: "email"
  /** Placeholder text */
  placeholder?: string
  /** Whether to validate email format */
  validate?: boolean
}

/**
 * URL field configuration
 */
export interface UrlFieldConfig extends BaseFieldConfig {
  type: "url"
  /** Placeholder text */
  placeholder?: string
  /** Whether to validate URL format */
  validate?: boolean
  /** Whether to show as clickable link */
  clickable?: boolean
}

/**
 * Badge/Tag field configuration
 */
export interface BadgeFieldConfig extends BaseFieldConfig {
  type: "badge"
  /** Predefined color variants */
  variants?: Record<string, {
    variant: "default" | "secondary" | "destructive" | "outline"
    color?: string
  }>
  /** Whether to show as multiple badges */
  multiple?: boolean
}

/**
 * Avatar field configuration
 */
export interface AvatarFieldConfig extends BaseFieldConfig {
  type: "avatar"
  /** Size of the avatar */
  size?: "sm" | "default" | "lg"
  /** Shape of the avatar */
  shape?: "circle" | "square"
  /** Fallback text field */
  fallbackField?: string
  /** Whether to show name alongside avatar */
  showName?: boolean
}

/**
 * Rating field configuration
 */
export interface RatingFieldConfig extends BaseFieldConfig {
  type: "rating"
  /** Maximum rating value */
  max?: number
  /** Whether half stars are allowed */
  allowHalf?: boolean
  /** Rating display variant */
  variant?: "stars" | "numeric" | "progress"
}

/**
 * Progress field configuration
 */
export interface ProgressFieldConfig extends BaseFieldConfig {
  type: "progress"
  /** Maximum value */
  max?: number
  /** Whether to show percentage */
  showPercentage?: boolean
  /** Color variant */
  variant?: "default" | "success" | "warning" | "danger"
}

/**
 * Custom field configuration for fully custom cell renderers
 */
export interface CustomFieldConfig extends BaseFieldConfig {
  type: "custom"
  /** Custom cell renderer */
  cellRenderer: (props: {
    value: any
    row: any
    field: CustomFieldConfig
  }) => React.ReactNode
  /** Custom filter component */
  filterRenderer?: (props: {
    value: any
    onChange: (value: any) => void
    field: CustomFieldConfig
  }) => React.ReactNode
}

/**
 * Union type of all field configurations
 */
export type DynamicFieldConfig =
  | TextFieldConfig
  | NumberFieldConfig
  | DateFieldConfig
  | BooleanFieldConfig
  | SelectFieldConfig
  | EmailFieldConfig
  | UrlFieldConfig
  | BadgeFieldConfig
  | AvatarFieldConfig
  | RatingFieldConfig
  | ProgressFieldConfig
  | CustomFieldConfig

/**
 * Dynamic table configuration
 */
export interface DynamicTableConfig<TData = any> {
  /** Array of field configurations */
  fields: DynamicFieldConfig[]
  /** Data accessor function for nested properties */
  accessor?: (data: TData, fieldId: string) => any
  /** Global field defaults */
  defaults?: Partial<BaseFieldConfig>
  /** Custom validation rules */
  validation?: Record<string, (value: any, data: TData) => boolean | string>
}

/**
 * Props for dynamic table component
 */
export interface DynamicTableViewProps<TData> {
  /** Dynamic table configuration */
  dynamicConfig: DynamicTableConfig<TData>
  /** Table data */
  data: TData[]
  /** Table configuration options */
  config?: {
    searchColumn?: string
    searchPlaceholder?: string
    pageSize?: number
    enableRowSelection?: boolean
    enableColumnVisibility?: boolean
    showToolbar?: boolean
    showPagination?: boolean
    variant?: "default" | "compact" | "spacious"
  }
  /** Toolbar configuration */
  toolbar?: {
    enableExport?: boolean
    onExport?: (format: "csv" | "excel" | "pdf") => void
    filters?: Array<{
      column: string
      title: string
      options: Array<{
        label: string
        value: string
        icon?: React.ComponentType<{ className?: string }>
      }>
    }>
    sorting?: Array<{
      column: string
      label: string
    }>
    extraActions?: React.ReactNode
  }
  /** Loading state */
  loading?: boolean
  /** Error message */
  error?: string | null
  /** Row click handler */
  onRowClick?: (row: TData) => void
  /** Row delete handler */
  onDeleteRow?: (row: TData) => void
  /** Show delete confirmation modal */
  showDeleteConfirm?: boolean
  /** Delete confirmation dialog title */
  deleteConfirmTitle?: string
  /** Delete confirmation dialog message */
  deleteConfirmMessage?: string
  /** Component class name */
  className?: string
  /** Test ID for the component */
  'data-test-id'?: string
  /** Test ID prefix for child elements */
  testIdPrefix?: string
}

/**
 * Context for field renderers
 */
export interface FieldRendererContext<TData = any> {
  /** The field configuration */
  field: DynamicFieldConfig
  /** The row data */
  row: TData
  /** The field value */
  value: any
  /** Whether the field is editable */
  editable?: boolean
  /** Change handler for editable fields */
  onChange?: (value: any) => void
  /** Validation errors */
  errors?: string[]
}