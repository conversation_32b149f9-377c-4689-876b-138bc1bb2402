"use client"

import { Table } from "@tanstack/react-table"
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react"

import { Button } from "@/registry/new-york-v4/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/registry/new-york-v4/ui/select"

interface TableViewPaginationProps<TData> {
  table: Table<TData>
  pageSizeOptions?: number[]
  showPageSizeSelector?: boolean
  /** Test ID prefix for child elements */
  testIdPrefix?: string
}

export function TableViewPagination<TData>({
  table,
  pageSizeOptions = [10, 20, 30, 40, 50],
  showPageSizeSelector = true,
  testIdPrefix,
}: TableViewPaginationProps<TData>) {
  const currentPage = table.getState().pagination.pageIndex + 1
  const totalPages = table.getPageCount()
  const pageSize = table.getState().pagination.pageSize
  const totalRows = table.getFilteredRowModel().rows.length
  const startRow = table.getState().pagination.pageIndex * pageSize + 1
  const endRow = Math.min(startRow + pageSize - 1, totalRows)

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between" data-test-id={`${testIdPrefix}_pagination`}>
      <div className="flex-1 text-sm text-muted-foreground" data-test-id={`${testIdPrefix}_pagination-info`}>
        {table.getFilteredSelectedRowModel().rows.length > 0 ? (
          <span data-test-id={`${testIdPrefix}_pagination-selected-info`}>
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected
          </span>
        ) : (
          <span data-test-id={`${testIdPrefix}_pagination-results-info`}>
            Showing {startRow} to {endRow} of {totalRows} results
          </span>
        )}
      </div>

      <div className="flex items-center gap-6 lg:gap-8" data-test-id={`${testIdPrefix}_pagination-controls`}>
        {showPageSizeSelector && (
          <div className="flex items-center gap-2" data-test-id={`${testIdPrefix}_page-size-selector`}>
            <p className="text-sm font-medium" data-test-id={`${testIdPrefix}_page-size-label`}>Rows per page</p>
            <Select
              value={`${pageSize}`}
              onValueChange={(value) => {
                table.setPageSize(Number(value))
              }}
              data-test-id={`${testIdPrefix}_page-size-select`}
            >
              <SelectTrigger className="h-8 w-[70px]" data-test-id={`${testIdPrefix}_page-size-trigger`}>
                <SelectValue placeholder={pageSize} data-test-id={`${testIdPrefix}_page-size-value`} />
              </SelectTrigger>
              <SelectContent side="top" data-test-id={`${testIdPrefix}_page-size-content`}>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={`${size}`} data-test-id={`${testIdPrefix}_page-size-option-${size}`}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex items-center gap-2" data-test-id={`${testIdPrefix}_page-navigation`}>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium" data-test-id={`${testIdPrefix}_page-info`}>
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center gap-2" data-test-id={`${testIdPrefix}_page-buttons`}>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
              data-test-id={`${testIdPrefix}_first-page-button`}
            >
              <span className="sr-only">Go to first page</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              data-test-id={`${testIdPrefix}_previous-page-button`}
            >
              <span className="sr-only">Go to previous page</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              data-test-id={`${testIdPrefix}_next-page-button`}
            >
              <span className="sr-only">Go to next page</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
              data-test-id={`${testIdPrefix}_last-page-button`}
            >
              <span className="sr-only">Go to last page</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}