"use client"

import * as React from "react"
import { Table } from "@tanstack/react-table"
import { Download, X, Search, SlidersHorizontal, BarChart3, Users, Activity, TrendingUp } from "lucide-react"

import { <PERSON><PERSON> } from "@/registry/new-york-v4/ui/button"
import { Input } from "@/registry/new-york-v4/ui/input"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/registry/new-york-v4/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/registry/new-york-v4/ui/select"

export interface FilterOption {
  label: string
  value: string
  icon?: React.ComponentType<{ className?: string }>
  iconName?: string
}

export interface TableViewToolbarProps<TData> {
  table: Table<TData>
  searchColumn?: string
  searchPlaceholder?: string
  enableColumnVisibility?: boolean
  enableExport?: boolean
  filters?: Array<{
    column: string
    title: string
    options: FilterOption[]
  }>
  sorting?: Array<{
    column: string
    label: string
  }>
  onExport?: (format: "csv" | "excel" | "pdf") => void
  extraActions?: React.ReactNode
  /** Test ID prefix for child elements */
  testIdPrefix?: string
}

// Helper function to render icons by name for App Router compatibility
const renderIconByName = (iconName: string) => {
  const iconMap = {
    BarChart3: BarChart3,
    Users: Users,
    Activity: Activity,
    TrendingUp: TrendingUp,
  }

  const IconComponent = iconMap[iconName as keyof typeof iconMap]
  return IconComponent ? <IconComponent className="h-4 w-4" /> : null
}

export function TableViewToolbar<TData>({
  table,
  searchColumn,
  searchPlaceholder = "Search...",
  enableColumnVisibility = true,
  enableExport = false,
  filters = [],
  sorting = [],
  onExport,
  extraActions,
  testIdPrefix,
}: TableViewToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const [sortBy, setSortBy] = React.useState<string>("")

  // Handle sorting change
  const handleSortChange = (value: string) => {
    setSortBy(value)
    if (value === "reset") {
      table.resetSorting()
      return
    }

    const [columnId, direction] = value.split("-")
    table.setSorting([{ id: columnId, desc: direction === "desc" }])
  }

  // Get current sort display value
  const getCurrentSortValue = () => {
    const sortState = table.getState().sorting
    if (sortState.length === 0) return ""

    const { id, desc } = sortState[0]
    return `${id}-${desc ? "desc" : "asc"}`
  }

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between" data-test-id={`${testIdPrefix}_toolbar`}>
      <div className="flex flex-1 items-center gap-2" data-test-id={`${testIdPrefix}_toolbar-left`}>
        {/* Search Input */}
        {searchColumn && (
          <div className="relative" data-test-id={`${testIdPrefix}_search-container`}>
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" data-test-id={`${testIdPrefix}_search-icon`} />
            <Input
              placeholder={searchPlaceholder}
              value={(table.getColumn(searchColumn)?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn(searchColumn)?.setFilterValue(event.target.value)
              }
              className="h-9 w-[200px] pl-8 lg:w-[300px]"
              data-test-id={`${testIdPrefix}_search-input`}
            />
          </div>
        )}

        {/* Filters */}
        {filters.map(({ column, title, options }) => {
          const tableColumn = table.getColumn(column)
          if (!tableColumn) return null

          return (
            <DropdownMenu key={column} data-test-id={`${testIdPrefix}_filter-${column}`}>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9" data-test-id={`${testIdPrefix}_filter-${column}-trigger`}>
                  <SlidersHorizontal className="mr-2 h-4 w-4" data-test-id={`${testIdPrefix}_filter-${column}-icon`} />
                  {title}
                  {Boolean(tableColumn.getFilterValue()) && (
                    <div className="ml-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground" data-test-id={`${testIdPrefix}_filter-${column}-count`}>
                      {Array.isArray(tableColumn.getFilterValue())
                        ? (tableColumn.getFilterValue() as string[]).length
                        : 1}
                    </div>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-[200px]" data-test-id={`${testIdPrefix}_filter-${column}-content`}>
                <DropdownMenuLabel data-test-id={`${testIdPrefix}_filter-${column}-label`}>{title}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {options.map((option) => {
                  const isSelected = Array.isArray(tableColumn.getFilterValue())
                    ? (tableColumn.getFilterValue() as string[])?.includes(option.value)
                    : tableColumn.getFilterValue() === option.value

                  return (
                    <DropdownMenuCheckboxItem
                      key={option.value}
                      className="capitalize"
                      checked={isSelected}
                      onCheckedChange={(checked) => {
                        const currentValue = tableColumn.getFilterValue() as string[] | undefined
                        if (checked) {
                          tableColumn.setFilterValue([...(currentValue || []), option.value])
                        } else {
                          tableColumn.setFilterValue(
                            currentValue?.filter((val) => val !== option.value) || []
                          )
                        }
                      }}
                      data-test-id={`${testIdPrefix}_filter-${column}-option-${option.value}`}
                    >
                      <div className="flex items-center gap-2">
                        {option.icon && <option.icon className="h-4 w-4" />}
                        {!option.icon && option.iconName && renderIconByName(option.iconName)}
                        {option.label}
                      </div>
                    </DropdownMenuCheckboxItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )
        })}

        {/* Sort Selector */}
        {sorting.length > 0 && (
          <Select value={getCurrentSortValue()} onValueChange={handleSortChange} data-test-id={`${testIdPrefix}_sort-select`}>
            <SelectTrigger className="h-9 w-[180px]" data-test-id={`${testIdPrefix}_sort-trigger`}>
              <SelectValue placeholder="Sort by..." data-test-id={`${testIdPrefix}_sort-value`} />
            </SelectTrigger>
            <SelectContent data-test-id={`${testIdPrefix}_sort-content`}>
              <SelectItem value="reset" data-test-id={`${testIdPrefix}_sort-reset`}>Default order</SelectItem>
              {sorting.map(({ column, label }) => (
                <React.Fragment key={column}>
                  <SelectItem value={`${column}-asc`} data-test-id={`${testIdPrefix}_sort-${column}-asc`}>{label} (A-Z)</SelectItem>
                  <SelectItem value={`${column}-desc`} data-test-id={`${testIdPrefix}_sort-${column}-desc`}>{label} (Z-A)</SelectItem>
                </React.Fragment>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Clear Filters */}
        {isFiltered && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => table.resetColumnFilters()}
            className="h-9 px-2 lg:px-3"
            data-test-id={`${testIdPrefix}_clear-filters-button`}
          >
            Reset
            <X className="ml-2 h-4 w-4" data-test-id={`${testIdPrefix}_clear-filters-icon`} />
          </Button>
        )}
      </div>

      <div className="flex items-center gap-2" data-test-id={`${testIdPrefix}_toolbar-right`}>
        {/* Extra Actions */}
        <div data-test-id={`${testIdPrefix}_extra-actions`}>
          {extraActions}
        </div>

        {/* Export Dropdown */}
        {enableExport && onExport && (
          <DropdownMenu data-test-id={`${testIdPrefix}_export-dropdown`}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9" data-test-id={`${testIdPrefix}_export-button`}>
                <Download className="mr-2 h-4 w-4" data-test-id={`${testIdPrefix}_export-icon`} />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-test-id={`${testIdPrefix}_export-content`}>
              <DropdownMenuLabel data-test-id={`${testIdPrefix}_export-label`}>Export as</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                onClick={() => onExport("csv")}
                data-test-id={`${testIdPrefix}_export-csv`}
              >
                CSV
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                onClick={() => onExport("excel")}
                data-test-id={`${testIdPrefix}_export-excel`}
              >
                Excel
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                onClick={() => onExport("pdf")}
                data-test-id={`${testIdPrefix}_export-pdf`}
              >
                PDF
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Column Visibility */}
        {enableColumnVisibility && (
          <DropdownMenu data-test-id={`${testIdPrefix}_column-visibility-dropdown`}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="ml-auto hidden h-9 lg:flex"
                data-test-id={`${testIdPrefix}_column-visibility-button`}
              >
                <SlidersHorizontal className="mr-2 h-4 w-4" data-test-id={`${testIdPrefix}_column-visibility-icon`} />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[150px]" data-test-id={`${testIdPrefix}_column-visibility-content`}>
              <DropdownMenuLabel data-test-id={`${testIdPrefix}_column-visibility-label`}>Toggle columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {table
                .getAllColumns()
                .filter(
                  (column) =>
                    typeof column.accessorFn !== "undefined" && column.getCanHide()
                )
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      data-test-id={`${testIdPrefix}_column-visibility-${column.id}`}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  )
}