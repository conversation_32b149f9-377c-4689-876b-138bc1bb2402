"use client"

import * as React from "react"
import { CalendarIcon, StarIcon, UserIcon, BuildingIcon, MailIcon } from "lucide-react"

import { Badge } from "@/registry/new-york-v4/ui/badge"
import { <PERSON><PERSON> } from "@/registry/new-york-v4/ui/button"

import { DynamicTableView, EnhancedTableView } from "../dynamic-table-view"
import { DynamicTableConfig } from "../types/dynamic-fields"

// Sample data types
export interface Employee {
  id: string
  firstName: string
  lastName: string
  email: string
  department: string
  role: string
  salary: number
  startDate: string
  isActive: boolean
  profileImage?: string
  performance: number
  projectCount: number
  website?: string
  skills: string[]
  manager?: {
    name: string
    id: string
  }
  settings: {
    notifications: boolean
    darkMode: boolean
  }
}

// Sample data
const sampleEmployees: Employee[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    department: "engineering",
    role: "Senior Developer",
    salary: 95000,
    startDate: "2022-03-15",
    isActive: true,
    profileImage: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face&auto=format",
    performance: 4.5,
    projectCount: 12,
    website: "https://johndoe.dev",
    skills: ["React", "TypeScript", "Node.js"],
    manager: {
      name: "Sarah Chen",
      id: "2"
    },
    settings: {
      notifications: true,
      darkMode: false
    }
  },
  {
    id: "2",
    firstName: "Sarah",
    lastName: "Chen",
    email: "<EMAIL>",
    department: "engineering",
    role: "Engineering Manager",
    salary: 125000,
    startDate: "2020-08-01",
    isActive: true,
    profileImage: "https://images.unsplash.com/photo-1494790108755-2616b612b789?w=32&h=32&fit=crop&crop=face&auto=format",
    performance: 4.8,
    projectCount: 25,
    skills: ["Leadership", "React", "System Design"],
    settings: {
      notifications: true,
      darkMode: true
    }
  },
  {
    id: "3",
    firstName: "Mike",
    lastName: "Johnson",
    email: "<EMAIL>",
    department: "design",
    role: "UX Designer",
    salary: 78000,
    startDate: "2023-01-10",
    isActive: true,
    performance: 4.2,
    projectCount: 8,
    website: "https://mikejohnson.design",
    skills: ["Figma", "Prototyping", "User Research"],
    manager: {
      name: "Lisa Park",
      id: "4"
    },
    settings: {
      notifications: false,
      darkMode: true
    }
  },
  {
    id: "4",
    firstName: "Lisa",
    lastName: "Park",
    email: "<EMAIL>",
    department: "design",
    role: "Design Director",
    salary: 115000,
    startDate: "2019-05-20",
    isActive: true,
    profileImage: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face&auto=format",
    performance: 4.7,
    projectCount: 35,
    skills: ["Design Strategy", "Team Leadership", "Figma"],
    settings: {
      notifications: true,
      darkMode: false
    }
  },
  {
    id: "5",
    firstName: "Alex",
    lastName: "Rodriguez",
    email: "<EMAIL>",
    department: "marketing",
    role: "Marketing Specialist",
    salary: 65000,
    startDate: "2023-06-01",
    isActive: false,
    performance: 3.8,
    projectCount: 5,
    skills: ["Content Marketing", "SEO", "Analytics"],
    manager: {
      name: "Jennifer Wu",
      id: "6"
    },
    settings: {
      notifications: false,
      darkMode: false
    }
  },
]

// Dynamic field configuration
const employeeTableConfig: DynamicTableConfig<Employee> = {
  fields: [
    {
      id: "profileImage",
      label: "Profile",
      type: "avatar",
      size: "default",
      fallbackField: "firstName",
      showName: false,
      sortable: false,
      width: 60,
    },
    {
      id: "firstName",
      label: "First Name",
      type: "text",
      required: true,
      sortable: true,
      filterable: true,
    },
    {
      id: "lastName",
      label: "Last Name",
      type: "text",
      required: true,
      sortable: true,
      filterable: true,
    },
    {
      id: "email",
      label: "Email",
      type: "email",
      sortable: true,
      filterable: true,
    },
    {
      id: "department",
      label: "Department",
      type: "select",
      options: [
        { label: "Engineering", value: "engineering", icon: BuildingIcon },
        { label: "Design", value: "design", icon: UserIcon },
        { label: "Marketing", value: "marketing", icon: MailIcon },
        { label: "Sales", value: "sales", icon: StarIcon },
      ],
      sortable: true,
      filterable: true,
    },
    {
      id: "role",
      label: "Role",
      type: "text",
      sortable: true,
      filterable: true,
    },
    {
      id: "salary",
      label: "Salary",
      type: "number",
      format: "currency",
      currency: "USD",
      precision: 0,
      sortable: true,
      filterable: false, // Salary filtering might be sensitive
    },
    {
      id: "startDate",
      label: "Start Date",
      type: "date",
      format: "MMM dd, yyyy",
      sortable: true,
      filterable: true,
    },
    {
      id: "isActive",
      label: "Status",
      type: "boolean",
      trueLabel: "Active",
      falseLabel: "Inactive",
      sortable: true,
      filterable: true,
    },
    {
      id: "performance",
      label: "Performance",
      type: "rating",
      max: 5,
      allowHalf: true,
      variant: "stars",
      sortable: true,
      filterable: false,
    },
    {
      id: "projectCount",
      label: "Projects",
      type: "number",
      format: "integer",
      sortable: true,
      filterable: false,
    },
    {
      id: "website",
      label: "Website",
      type: "url",
      clickable: true,
      sortable: false,
      filterable: false,
      hideable: true,
      defaultVisible: false,
    },
    {
      id: "skills",
      label: "Skills",
      type: "badge",
      multiple: true,
      variants: {
        "React": { variant: "default" },
        "TypeScript": { variant: "secondary" },
        "Node.js": { variant: "outline" },
        "Figma": { variant: "default" },
        "Leadership": { variant: "destructive" },
      },
      sortable: false,
      filterable: true,
      hideable: true,
      defaultVisible: false,
    },
    {
      id: "manager.name",
      label: "Manager",
      type: "text",
      sortable: true,
      filterable: true,
      hideable: true,
      defaultVisible: false,
    },
    {
      id: "settings.notifications",
      label: "Notifications",
      type: "boolean",
      variant: "switch",
      trueLabel: "On",
      falseLabel: "Off",
      sortable: false,
      filterable: true,
      hideable: true,
      defaultVisible: false,
    },
  ],
  defaults: {
    sortable: true,
    filterable: true,
    hideable: true,
    defaultVisible: true,
  },
  // Custom accessor for nested properties
  accessor: (data, fieldId) => {
    if (fieldId.includes(".")) {
      return fieldId.split(".").reduce((obj, key) => obj?.[key], data as any)
    }
    return (data as any)[fieldId]
  },
}

export function DynamicFieldsTable() {
  const [data] = React.useState(sampleEmployees)

  const handleExport = (format: "csv" | "excel" | "pdf") => {
    console.log(`Exporting employee data as ${format}`)
    // Implement actual export logic here
  }

  const handleRowClick = (employee: Employee) => {
    console.log("Clicked employee:", employee)
    // Handle row click - maybe open employee details
  }

  const handleDeleteEmployee = (employee: Employee) => {
    console.log("Delete employee:", employee)
    // Implement delete logic here
  }

  return (
    <div className="space-y-4" data-test-id="employee_directory">
      <div className="flex items-center justify-between" data-test-id="employee-directory_header">
        <div data-test-id="employee-directory_title-section">
          <h2 className="text-2xl font-bold tracking-tight" data-test-id="employee-directory_title">Employee Directory</h2>
          <p className="text-muted-foreground" data-test-id="employee-directory_description">
            Manage employee information with dynamic fields
          </p>
        </div>
        <div className="flex items-center gap-2" data-test-id="employee-directory_stats">
          <Badge variant="outline" data-test-id="employee-directory_total-count">
            Total Employees: {data.length}
          </Badge>
          <Badge variant="outline" data-test-id="employee-directory_active-count">
            Active: {data.filter(emp => emp.isActive).length}
          </Badge>
        </div>
      </div>

      <DynamicTableView
        dynamicConfig={employeeTableConfig}
        data={data}
        config={{
          searchColumn: "firstName", // Will search first name
          searchPlaceholder: "Search employees...",
          pageSize: 10,
          enableRowSelection: true,
          enableColumnVisibility: true,
          variant: "default",
        }}
        toolbar={{
          enableExport: true,
          onExport: handleExport,
          extraActions: (
            <div className="flex items-center gap-2" data-test-id="employee-directory_actions">
              <Button variant="outline" size="sm" data-test-id="employee-directory_add-button">
                Add Employee
              </Button>
              <Button variant="outline" size="sm" data-test-id="employee-directory_import-button">
                Import CSV
              </Button>
            </div>
          ),
        }}
        onRowClick={handleRowClick}
        onDeleteRow={handleDeleteEmployee}
        showDeleteConfirm={true}
        deleteConfirmTitle="Delete Employee"
        deleteConfirmMessage="Are you sure you want to delete this employee? This action cannot be undone."
        data-test-id="employee-directory_table"
        testIdPrefix="employee-directory"
      />
    </div>
  )
}

// Example of a simpler dynamic configuration
export interface Product {
  id: string
  name: string
  price: number
  category: string
  inStock: boolean
  rating: number
  lastUpdated: string
}

const productTableConfig: DynamicTableConfig<Product> = {
  fields: [
    {
      id: "name",
      label: "Product Name",
      type: "text",
      sortable: true,
      filterable: true,
    },
    {
      id: "price",
      label: "Price",
      type: "number",
      format: "currency",
      currency: "USD",
      precision: 2,
      sortable: true,
    },
    {
      id: "category",
      label: "Category",
      type: "select",
      options: [
        { label: "Electronics", value: "electronics" },
        { label: "Clothing", value: "clothing" },
        { label: "Home & Garden", value: "home" },
        { label: "Sports", value: "sports" },
      ],
      filterable: true,
    },
    {
      id: "inStock",
      label: "In Stock",
      type: "boolean",
      trueLabel: "Available",
      falseLabel: "Out of Stock",
      filterable: true,
    },
    {
      id: "rating",
      label: "Rating",
      type: "rating",
      max: 5,
      allowHalf: true,
      variant: "stars",
    },
    {
      id: "lastUpdated",
      label: "Last Updated",
      type: "date",
      format: "PPp",
      sortable: true,
    },
  ],
}

export function SimpleProductTable() {
  const sampleProducts: Product[] = [
    {
      id: "1",
      name: "Wireless Headphones",
      price: 199.99,
      category: "electronics",
      inStock: true,
      rating: 4.5,
      lastUpdated: "2024-01-15T10:30:00Z",
    },
    {
      id: "2",
      name: "Running Shoes",
      price: 89.99,
      category: "sports",
      inStock: false,
      rating: 4.2,
      lastUpdated: "2024-01-14T15:45:00Z",
    },
    // Add more sample products as needed
  ]

  return (
    <div className="space-y-4" data-test-id="product_catalog">
      <div data-test-id="product-catalog_header">
        <h2 className="text-2xl font-bold tracking-tight" data-test-id="product-catalog_title">Product Catalog</h2>
        <p className="text-muted-foreground" data-test-id="product-catalog_description">
          Simple product listing with dynamic fields
        </p>
      </div>

      <DynamicTableView
        dynamicConfig={productTableConfig}
        data={sampleProducts}
        config={{
          searchColumn: "name",
          searchPlaceholder: "Search products...",
          pageSize: 25,
          enableRowSelection: false,
          variant: "compact",
        }}
        toolbar={{
          enableExport: true,
          onExport: (format) => console.log(`Export products as ${format}`),
        }}
        data-test-id="product-catalog_table"
        testIdPrefix="product-catalog"
      />
    </div>
  )
}