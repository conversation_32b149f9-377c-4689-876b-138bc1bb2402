"use client"

import * as React from "react"
import { ColumnDef } from "@tanstack/react-table"

import { cn } from "@/lib/utils"

import { TableView, TableViewProps, TableViewConfig } from "./table-view"
import { TableViewToolbarProps } from "./table-view-toolbar"
import {
  DynamicTableConfig,
  DynamicTableViewProps,
} from "./types/dynamic-fields"
import {
  createColumnsFromDynamicConfig,
  createFilterConfigFromFields,
  createSortingConfigFromFields,
  validateDynamicConfig,
} from "./utils/dynamic-columns"

/**
 * Enhanced TableView component that supports dynamic field configurations
 */
export function DynamicTableView<TData>({
  dynamicConfig,
  data,
  config = {},
  toolbar = {},
  loading = false,
  error = null,
  onRowClick,
  onDeleteRow,
  className,
  'data-test-id': testId,
  testIdPrefix,
  ...props
}: DynamicTableViewProps<TData>) {
  // Validate the dynamic configuration
  const validation = React.useMemo(() => {
    return validateDynamicConfig(dynamicConfig)
  }, [dynamicConfig])

  // Show validation errors
  if (!validation.valid) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center" data-test-id={`${testId || testIdPrefix}_configuration-error`}>
        <div className="text-destructive text-sm mb-2" data-test-id={`${testId || testIdPrefix}_error-title`}>
          Configuration Error
        </div>
        <div className="text-muted-foreground text-xs space-y-1" data-test-id={`${testId || testIdPrefix}_error-messages`}>
          {validation.errors.map((error, index) => (
            <div key={index} data-test-id={`${testId || testIdPrefix}_error-message-${index}`}>{error}</div>
          ))}
        </div>
      </div>
    )
  }

  // Generate columns from dynamic configuration
  const columns = React.useMemo(() => {
    return createColumnsFromDynamicConfig(
      dynamicConfig,
      config.enableRowSelection
    )
  }, [dynamicConfig, config.enableRowSelection])

  // Generate filter configuration
  const filterConfig = React.useMemo(() => {
    return createFilterConfigFromFields(dynamicConfig.fields)
  }, [dynamicConfig.fields])

  // Generate sorting configuration
  const sortingConfig = React.useMemo(() => {
    return createSortingConfigFromFields(dynamicConfig.fields)
  }, [dynamicConfig.fields])

  // Enhanced toolbar configuration
  const enhancedToolbar: Partial<TableViewToolbarProps<TData>> = {
    ...toolbar,
    filters: [
      ...(toolbar.filters || []),
      ...filterConfig,
    ],
    sorting: [
      ...(toolbar.sorting || []),
      ...sortingConfig,
    ],
  }

  // Find the first text field for search
  const searchColumn = React.useMemo(() => {
    if (config.searchColumn) return config.searchColumn

    const textField = dynamicConfig.fields.find(
      field => field.type === "text" || field.type === "email"
    )
    return textField?.id
  }, [config.searchColumn, dynamicConfig.fields])

  // Enhanced table configuration
  const enhancedConfig: TableViewConfig = {
    ...config,
    searchColumn,
  }

  return (
    <TableView
      columns={columns as ColumnDef<TData, any>[]}
      data={data}
      config={enhancedConfig}
      toolbar={enhancedToolbar}
      loading={loading}
      error={error}
      onRowClick={onRowClick}
      onDeleteRow={onDeleteRow}
      className={cn("dynamic-table-view", className)}
      data-test-id={testId}
      testIdPrefix={testIdPrefix}
      {...props}
    />
  )
}

/**
 * Enhanced TableView component that supports both static columns and dynamic configuration
 * This maintains backward compatibility while adding dynamic field support
 */
export interface EnhancedTableViewProps<TData, TValue = any>
  extends Omit<TableViewProps<TData, TValue>, "columns"> {
  /** Traditional static column definitions (for backward compatibility) */
  columns?: ColumnDef<TData, TValue>[]
  /** New dynamic field configuration */
  dynamicConfig?: DynamicTableConfig<TData>
  /** Test ID for the component */
  'data-test-id'?: string
  /** Test ID prefix for child elements */
  testIdPrefix?: string
}

export function EnhancedTableView<TData, TValue = any>({
  columns,
  dynamicConfig,
  data,
  config = {},
  toolbar = {},
  loading = false,
  error = null,
  onRowClick,
  onDeleteRow,
  className,
  'data-test-id': testId,
  testIdPrefix,
  ...props
}: EnhancedTableViewProps<TData, TValue>) {
  // Validation: Must provide either columns or dynamicConfig
  if (!columns && !dynamicConfig) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center" data-test-id={`${testId || testIdPrefix}_configuration-error`}>
        <div className="text-destructive text-sm" data-test-id={`${testId || testIdPrefix}_error-title`}>
          Configuration Error
        </div>
        <div className="text-muted-foreground text-xs" data-test-id={`${testId || testIdPrefix}_error-message`}>
          Must provide either columns or dynamicConfig
        </div>
      </div>
    )
  }

  // If both are provided, prefer static columns with a warning
  if (columns && dynamicConfig) {
    console.warn(
      "EnhancedTableView: Both columns and dynamicConfig provided. Using static columns. " +
      "Consider using only one configuration method."
    )
  }

  // Use static columns if provided
  if (columns) {
    return (
      <TableView
        columns={columns}
        data={data}
        config={config}
        toolbar={toolbar}
        loading={loading}
        error={error}
        onRowClick={onRowClick}
        onDeleteRow={onDeleteRow}
        className={className}
        data-test-id={testId}
        testIdPrefix={testIdPrefix}
        {...props}
      />
    )
  }

  // Use dynamic configuration
  return (
    <DynamicTableView
      dynamicConfig={dynamicConfig!}
      data={data}
      config={config}
      toolbar={toolbar}
      loading={loading}
      error={error}
      onRowClick={onRowClick}
      onDeleteRow={onDeleteRow}
      className={className}
      data-test-id={testId}
      testIdPrefix={testIdPrefix}
      {...props}
    />
  )
}

// Export types for external use
export type {
  DynamicTableConfig,
  DynamicTableViewProps,
}

// Re-export field types for convenience
export type {
  DynamicFieldConfig,
  TextFieldConfig,
  NumberFieldConfig,
  DateFieldConfig,
  BooleanFieldConfig,
  SelectFieldConfig,
  EmailFieldConfig,
  UrlFieldConfig,
  BadgeFieldConfig,
  AvatarFieldConfig,
  RatingFieldConfig,
  ProgressFieldConfig,
  CustomFieldConfig,
} from "./types/dynamic-fields"