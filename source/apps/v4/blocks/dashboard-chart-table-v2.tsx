"use client"

import * as React from "react"
import { TrendingUp, TrendingDown, Users, Activity, DollarSign, BarChart3 } from "lucide-react"
import { Pie, PieChart } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/registry/new-york-v4/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/registry/new-york-v4/ui/chart"
import { TableView, type TableViewProps } from "@/components/table-view"

// Sample data for pie charts - adapted from the original chart data
const usersChartData = [
  { name: "New Users", value: 350, fill: "#3b82f6" },
  { name: "Returning", value: 480, fill: "#60a5fa" },
  { name: "Inactive", value: 120, fill: "#93c5fd" },
]

const activeSessionsData = [
  { name: "Desktop", value: 203, fill: "#2563eb" },
  { name: "Mobile", value: 156, fill: "#3b82f6" },
  { name: "Tablet", value: 89, fill: "#60a5fa" },
]

const revenueSourcesData = [
  { name: "Direct Sales", value: 28000, fill: "#1d4ed8" },
  { name: "Subscriptions", value: 19500, fill: "#3b82f6" },
  { name: "Partnerships", value: 12800, fill: "#60a5fa" },
  { name: "Referrals", value: 8200, fill: "#93c5fd" },
]

// Chart configurations for pie charts
const usersChartConfig = {
  value: {
    label: "Users",
  },
  newUsers: {
    label: "New Users",
    color: "#3b82f6",
  },
  returning: {
    label: "Returning",
    color: "#60a5fa",
  },
  inactive: {
    label: "Inactive",
    color: "#93c5fd",
  },
} satisfies ChartConfig

const activeChartConfig = {
  value: {
    label: "Sessions",
  },
  desktop: {
    label: "Desktop",
    color: "#2563eb",
  },
  mobile: {
    label: "Mobile",
    color: "#3b82f6",
  },
  tablet: {
    label: "Tablet",
    color: "#60a5fa",
  },
} satisfies ChartConfig

const revenueChartConfig = {
  value: {
    label: "Revenue",
  },
  direct: {
    label: "Direct Sales",
    color: "#1d4ed8",
  },
  subscriptions: {
    label: "Subscriptions",
    color: "#3b82f6",
  },
  partnerships: {
    label: "Partnerships",
    color: "#60a5fa",
  },
  referrals: {
    label: "Referrals",
    color: "#93c5fd",
  },
} satisfies ChartConfig

interface SummaryCardProps {
  title: string
  description?: string
  variant?: "default" | "compact"
  "data-test-id"?: string
}

function SummaryCard({
  title,
  description,
  variant = "default",
  "data-test-id": testId
}: SummaryCardProps) {
  return (
    <Card className="border-blue-200" data-test-id={testId}>
      <CardHeader className={variant === "compact" ? "pb-2" : "pb-3"}>
        <div className="flex items-center justify-between">
          <CardDescription className="text-sm font-medium text-gray-600">{title}</CardDescription>
          <div className="h-4 w-4 text-blue-600" data-test-id={`${testId}_icon`}>
            <DollarSign />
          </div>
        </div>
        {description && (
          <CardDescription className="text-xs text-gray-600" data-test-id={`${testId}_description`}>
            {description}
          </CardDescription>
        )}
      </CardHeader>
    </Card>
  )
}

interface PieChartCardProps {
  title: string
  data: any[]
  config: ChartConfig
  description?: string
  footer?: string
  variant?: "default" | "compact"
  "data-test-id"?: string
}

function PieChartCard({
  title,
  data,
  config,
  description,
  footer,
  variant = "default",
  "data-test-id": testId
}: PieChartCardProps) {
  const chartHeight = variant === "compact" ? "h-[120px]" : "h-[200px]"

  return (
    <Card className="border-blue-200" data-test-id={testId}>
      <CardHeader className={variant === "compact" ? "pb-2" : "pb-3"}>
        <CardTitle className={variant === "compact" ? "text-base" : "text-lg"} data-test-id={`${testId}_title`}>
          {title}
        </CardTitle>
        {description && (
          <CardDescription className="text-sm text-gray-600" data-test-id={`${testId}_description`}>
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="pb-0">
        <ChartContainer config={config} className={`w-full ${chartHeight}`} data-test-id={`${testId}_chart`}>
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={data}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              outerRadius={variant === "compact" ? 40 : 60}
              innerRadius={variant === "compact" ? 20 : 30}
              strokeWidth={0}
            />
          </PieChart>
        </ChartContainer>
        {footer && (
          <div className="pt-3 text-xs text-gray-600" data-test-id={`${testId}_footer`}>
            {footer}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export interface DashboardChartTableV2Props<TData = any, TValue = any> {
  // Summary section props
  summaryData?: {
    totalRevenue?: number
    revenueChange?: number
    totalUsers?: number
    usersChange?: number
    activeUsers?: number
    activeChange?: number
    conversionRate?: number
    conversionChange?: number
  }

  // Chart section props
  showCharts?: boolean
  chartVariant?: "default" | "compact"

  // Table section props
  tableProps: Omit<TableViewProps<TData, TValue>, "data-test-id" | "testIdPrefix">

  // Layout props
  className?: string
  sectionGap?: "sm" | "md" | "lg"

  // Test coverage
  "data-test-id"?: string
  testIdPrefix?: string
}

export function DashboardChartTableV2<TData = any, TValue = any>({
  summaryData,
  showCharts = true,
  chartVariant = "default",
  tableProps,
  className,
  sectionGap = "md",
  "data-test-id": testId,
  testIdPrefix,
}: DashboardChartTableV2Props<TData, TValue>) {
  const gapClass = {
    sm: "gap-4",
    md: "gap-6",
    lg: "gap-8",
  }[sectionGap]

  const baseTestId = testId || testIdPrefix || "dashboard-chart-table-v2"

  return (
    <div className={`flex flex-col ${gapClass} ${className || ""}`} data-test-id={baseTestId}>
      {/* First Section: 4 columns layout with summary card + 3 pie charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-test-id={`${baseTestId}_summary-section`}>
        {/* Summary Card - No count numbers, just metric description */}
        <SummaryCard
          title="Total Revenue"
          description="Monthly recurring revenue and growth metrics"
          variant={chartVariant}
          data-test-id={`${baseTestId}_revenue-card`}
        />

        {/* Pie Chart Components */}
        {showCharts && (
          <>
            <PieChartCard
              title="User Distribution"
              data={usersChartData}
              config={usersChartConfig}
              description="User segmentation breakdown"
              footer="Based on 6-month activity"
              variant={chartVariant}
              data-test-id={`${baseTestId}_users-pie-chart`}
            />

            <PieChartCard
              title="Session Sources"
              data={activeSessionsData}
              config={activeChartConfig}
              description="Active sessions by device type"
              footer="Real-time data analysis"
              variant={chartVariant}
              data-test-id={`${baseTestId}_sessions-pie-chart`}
            />

            <PieChartCard
              title="Revenue Sources"
              data={revenueSourcesData}
              config={revenueChartConfig}
              description="Revenue breakdown by channel"
              footer="Monthly performance metrics"
              variant={chartVariant}
              data-test-id={`${baseTestId}_revenue-pie-chart`}
            />
          </>
        )}
      </div>

      {/* Second Section: Table view content */}
      <div className="w-full" data-test-id={`${baseTestId}_table-section`}>
        <TableView
          {...tableProps}
          data-test-id={`${baseTestId}_table`}
          testIdPrefix={`${baseTestId}_table`}
        />
      </div>
    </div>
  )
}

export default DashboardChartTableV2