"use client"

import * as React from "react"
import { TrendingUp, TrendingDown, Users, Activity, DollarSign, BarChart3 } from "lucide-react"
import { Bar, BarChart, CartesianGrid, XAxis, Area, AreaChart, Line, LineChart } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/registry/new-york-v4/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/registry/new-york-v4/ui/chart"
import { TableView, type TableViewProps } from "@/components/table-view"

// Sample data for charts
const defaultSummaryData = {
  totalRevenue: 45231.89,
  revenueChange: 20.1,
  totalUsers: 2350,
  usersChange: 15.2,
  activeUsers: 573,
  activeChange: -2.4,
  conversionRate: 3.2,
  conversionChange: 8.1,
}

const defaultChartData = [
  { month: "Jan", revenue: 15400, users: 240, active: 89 },
  { month: "Feb", revenue: 18200, users: 380, active: 156 },
  { month: "Mar", revenue: 12800, users: 290, active: 134 },
  { month: "Apr", revenue: 22000, users: 420, active: 187 },
  { month: "May", revenue: 19500, users: 350, active: 165 },
  { month: "Jun", revenue: 28000, users: 480, active: 203 },
]

const revenueChartConfig = {
  revenue: {
    label: "Revenue",
    color: "#3b82f6",
  },
} satisfies ChartConfig

const usersChartConfig = {
  users: {
    label: "Users",
    color: "#3b82f6",
  },
} satisfies ChartConfig

const activeChartConfig = {
  active: {
    label: "Active",
    color: "#2563eb",
  },
} satisfies ChartConfig

interface SummaryCardProps {
  title: string
  value: string | number
  change: number
  icon: React.ReactNode
  description?: string
  variant?: "default" | "compact"
  "data-test-id"?: string
}

function SummaryCard({
  title,
  value,
  change,
  icon,
  description,
  variant = "default",
  "data-test-id": testId
}: SummaryCardProps) {
  const isPositive = change > 0
  const changeIcon = isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />
  const changeColor = isPositive ? "text-blue-600" : "text-blue-600"

  return (
    <Card data-test-id={testId}>
      <CardHeader className={variant === "compact" ? "pb-2" : "pb-3"}>
        <div className="flex items-center justify-between">
          <CardDescription className="text-sm font-medium">{title}</CardDescription>
          <div className="h-4 w-4 text-muted-foreground" data-test-id={`${testId}_icon`}>
            {icon}
          </div>
        </div>
        <CardTitle className={variant === "compact" ? "text-2xl" : "text-3xl"} data-test-id={`${testId}_value`}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </CardTitle>
        <div className={`flex items-center gap-1 text-sm ${changeColor}`} data-test-id={`${testId}_change`}>
          {changeIcon}
          <span>{Math.abs(change)}% from last month</span>
        </div>
        {description && (
          <CardDescription className="text-xs text-muted-foreground" data-test-id={`${testId}_description`}>
            {description}
          </CardDescription>
        )}
      </CardHeader>
    </Card>
  )
}

interface ChartCardProps {
  title: string
  chartType: "bar" | "area" | "line"
  data: any[]
  config: ChartConfig
  dataKey: string
  description?: string
  footer?: string
  variant?: "default" | "compact"
  "data-test-id"?: string
}

function ChartCard({
  title,
  chartType,
  data,
  config,
  dataKey,
  description,
  footer,
  variant = "default",
  "data-test-id": testId
}: ChartCardProps) {
  const chartHeight = variant === "compact" ? "h-[120px]" : "h-[200px]"

  const renderChart = () => {
    const chartProps = {
      data,
      margin: {
        top: 5,
        right: 10,
        left: 10,
        bottom: 0,
      },
    }

    switch (chartType) {
      case "bar":
        return (
          <BarChart {...chartProps}>
            <CartesianGrid vertical={false} strokeDasharray="3 3" />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              fontSize={12}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Bar
              dataKey={dataKey}
              fill={Object.values(config)[0]?.color}
              radius={4}
            />
          </BarChart>
        )
      case "area":
        return (
          <AreaChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              fontSize={12}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area
              dataKey={dataKey}
              fill={Object.values(config)[0]?.color}
              fillOpacity={0.1}
              stroke={Object.values(config)[0]?.color}
              strokeWidth={2}
              type="monotone"
            />
          </AreaChart>
        )
      case "line":
        return (
          <LineChart {...chartProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              fontSize={12}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              strokeWidth={2}
              dataKey={dataKey}
              stroke={Object.values(config)[0]?.color}
              activeDot={{ r: 4 }}
            />
          </LineChart>
        )
      default:
        return null
    }
  }

  return (
    <Card data-test-id={testId}>
      <CardHeader className={variant === "compact" ? "pb-2" : "pb-3"}>
        <CardTitle className={variant === "compact" ? "text-base" : "text-lg"} data-test-id={`${testId}_title`}>
          {title}
        </CardTitle>
        {description && (
          <CardDescription className="text-sm" data-test-id={`${testId}_description`}>
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="pb-0">
        <ChartContainer config={config} className={`w-full ${chartHeight}`} data-test-id={`${testId}_chart`}>
          {renderChart()}
        </ChartContainer>
        {footer && (
          <div className="pt-3 text-xs text-muted-foreground" data-test-id={`${testId}_footer`}>
            {footer}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export interface DashboardChartTableProps<TData = any, TValue = any> {
  // Summary section props
  summaryData?: {
    totalRevenue?: number
    revenueChange?: number
    totalUsers?: number
    usersChange?: number
    activeUsers?: number
    activeChange?: number
    conversionRate?: number
    conversionChange?: number
  }

  // Chart section props
  chartData?: any[]
  showCharts?: boolean
  chartVariant?: "default" | "compact"

  // Table section props
  tableProps: Omit<TableViewProps<TData, TValue>, "data-test-id" | "testIdPrefix">

  // Layout props
  className?: string
  sectionGap?: "sm" | "md" | "lg"

  // Test coverage
  "data-test-id"?: string
  testIdPrefix?: string
}

export function DashboardChartTable<TData = any, TValue = any>({
  summaryData: propSummaryData,
  chartData: propChartData = defaultChartData,
  showCharts = true,
  chartVariant = "default",
  tableProps,
  className,
  sectionGap = "md",
  "data-test-id": testId,
  testIdPrefix,
}: DashboardChartTableProps<TData, TValue>) {
  const gapClass = {
    sm: "gap-4",
    md: "gap-6",
    lg: "gap-8",
  }[sectionGap]

  const baseTestId = testId || testIdPrefix || "dashboard-chart-table"

  return (
    <div className={`flex flex-col ${gapClass} ${className || ""}`} data-test-id={baseTestId}>
      {/* First Section: 4 columns layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-test-id={`${baseTestId}_summary-section`}>
        {/* Summary Card */}
        <SummaryCard
          title="Total Revenue"
          value={`$${(propSummaryData || defaultSummaryData).totalRevenue?.toLocaleString() || "0"}`}
          change={(propSummaryData || defaultSummaryData).revenueChange || 0}
          icon={<DollarSign />}
          description="Monthly recurring revenue"
          variant={chartVariant}
          data-test-id={`${baseTestId}_revenue-card`}
        />

        {/* Chart Components for counting numbers */}
        {showCharts && (
          <>
            <ChartCard
              title="New Users"
              chartType="bar"
              data={propChartData}
              config={usersChartConfig}
              dataKey="users"
              description="User registrations"
              footer="6-month trend"
              variant={chartVariant}
              data-test-id={`${baseTestId}_users-chart`}
            />

            <ChartCard
              title="Active Sessions"
              chartType="area"
              data={propChartData}
              config={activeChartConfig}
              dataKey="active"
              description="Daily active users"
              footer="Real-time data"
              variant={chartVariant}
              data-test-id={`${baseTestId}_active-chart`}
            />

            <ChartCard
              title="Revenue Trend"
              chartType="line"
              data={propChartData}
              config={revenueChartConfig}
              dataKey="revenue"
              description="Monthly performance"
              footer="Growth metrics"
              variant={chartVariant}
              data-test-id={`${baseTestId}_revenue-chart`}
            />
          </>
        )}
      </div>

      {/* Second Section: Table view content */}
      <div className="w-full" data-test-id={`${baseTestId}_table-section`}>
        <TableView
          {...tableProps}
          data-test-id={`${baseTestId}_table`}
          testIdPrefix={`${baseTestId}_table`}
        />
      </div>
    </div>
  )
}

export default DashboardChartTable