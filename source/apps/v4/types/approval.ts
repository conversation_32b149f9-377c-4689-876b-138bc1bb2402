// Approval management types following the established patterns

export interface ApprovalFormData {
  title: string
  description: string
  submitter: string
  approver: string
  category: string
  priority: string
  status: string
  requestedAmount?: number
  businessJustification: string
  attachmentUrl?: string
}

export interface ApprovalData extends ApprovalFormData {
  id: string
  submittedAt: string
  reviewedAt?: string
  approvedAt?: string
  rejectedAt?: string
  updatedAt: string
  notes?: string[]
  workflowStage: string
  estimatedValue?: number
  department: string
  urgency: string
}

export interface ApprovalTableFilters {
  status: string[]
  category: string[]
  priority: string[]
  approver: string[]
  department: string[]
}

// Following established patterns from user and expense types
export const APPROVAL_CATEGORIES = [
  "Budget Request",
  "Vendor Selection",
  "Capital Expenditure",
  "Personnel Hiring",
  "Policy Change",
  "Contract Approval",
  "Project Authorization",
  "Equipment Purchase"
] as const

export const APPROVAL_STATUSES = [
  "Draft",
  "Submitted",
  "Under Review",
  "Pending Approval",
  "Approved",
  "Rejected",
  "Withdrawn",
  "Expired"
] as const

export const APPROVAL_PRIORITIES = [
  "Low",
  "Medium",
  "High",
  "Critical"
] as const

export const APPROVAL_WORKFLOW_STAGES = [
  "Initial Review",
  "Manager Approval",
  "Finance Review",
  "Final Approval",
  "Implementation"
] as const

export const APPROVAL_DEPARTMENTS = [
  { label: "Engineering", value: "engineering" },
  { label: "Marketing", value: "marketing" },
  { label: "Sales", value: "sales" },
  { label: "HR", value: "hr" },
  { label: "Finance", value: "finance" },
  { label: "Operations", value: "operations" },
  { label: "Legal", value: "legal" },
  { label: "Executive", value: "executive" }
] as const

export const APPROVAL_APPROVERS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
] as const

export const APPROVAL_URGENCY_LEVELS = [
  { label: "Standard", value: "standard" },
  { label: "Expedited", value: "expedited" },
  { label: "Emergency", value: "emergency" }
] as const