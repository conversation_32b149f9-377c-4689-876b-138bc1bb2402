<mxfile host="65bd71144e">
    <diagram name="SME Accounting System Event Storming" id="sme-accounting-system">
        <mxGraphModel dx="4780" dy="3273" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="1169" pageHeight="827" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="Bc7mP2wQ" value="Expense Management" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="-166" width="4500" height="1816" as="geometry">
                        <mxRectangle x="50" y="50" width="200" height="26" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="Fn9BtYz3" value="Employee Expense Request Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Bc7mP2wQ" vertex="1">
                    <mxGeometry x="30" y="50" width="2000" height="397" as="geometry"/>
                </mxCell>
                <object label="Expense&#xa;Dashboard View" type="read_model" id="Rd4VcH8s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="30" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Form" type="ui" id="Mt8KeP5x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="200" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Employee" type="actor" id="Qx2NvB7k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="370" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Create Expense&#xa;Request" type="action" input="amount: number&#xa;category: string&#xa;receipt: file&#xa;description: string&#xa;projectId: string" success="requestId: string" error="VALIDATION_ERROR&#xa;FILE_TOO_LARGE&#xa;INVALID_PROJECT" id="Lw6SpM9r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="540" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Processing Policy" type="policy" id="Nw9HmP3k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="708" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Submitted" type="event" id="Kf5WbN2t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="878" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge001" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" source="Rd4VcH8s" target="Mt8KeP5x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge002" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" source="Mt8KeP5x" target="Qx2NvB7k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge003" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" source="Qx2NvB7k" target="Lw6SpM9r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge004" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" source="Lw6SpM9r" target="Nw9HmP3k" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="710" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge006" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" target="Jx4DnE6p" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1000" y="140" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge009" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" target="Bx5QmL7r" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1510" y="140" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge011" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Fn9BtYz3" source="Nw9HmP3k" target="Kf5WbN2t" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="OCR System&#xa;(Mock in MVP)" type="external_system" id="11">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="705" y="277" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Processing Policy" type="policy" id="15">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1002" y="277" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Submitted" type="event" id="16">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1172" y="277" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="23" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="11" target="15" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1222" y="337" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="15" target="16" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Gm8PvX3c" value="Budget Validation &amp; Approval Routing Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Bc7mP2wQ" vertex="1">
                    <mxGeometry x="34" y="674" width="2300" height="350" as="geometry"/>
                </mxCell>
                <object label="Budget Validation&lt;br&gt;Policy&lt;div&gt;AND&amp;nbsp;&lt;br&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Workflow&lt;/span&gt;&lt;br style=&quot;color: rgb(63, 63, 63); scrollbar-color: rgb(226, 226, 226) rgb(251, 251, 251);&quot;&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Configuration&lt;/span&gt;&lt;br style=&quot;color: rgb(63, 63, 63); scrollbar-color: rgb(226, 226, 226) rgb(251, 251, 251);&quot;&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Policy&lt;/span&gt;&lt;/div&gt;" type="reaction_policy" id="Tv2YbM5q">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="30" style="edgeStyle=none;html=1;" edge="1" parent="Gm8PvX3c" source="Qp5VnR7x" target="Rw9HmP5t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="Determine&#xa;Approval Route" type="action" input="expenseId: string&#xa;amount: number&#xa;budgetStatus: object&#xa;category: string" success="approvalRoute: array" error="ROUTING_FAILED" id="Qp5VnR7x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="227" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Approval Route&#xa;Assigned" type="event" id="Rw9HmP5t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="567" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Notification&#xa;Policy" type="reaction_policy" id="Yx3KpL8v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="737" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Send Approval&#xa;Notification" type="action" input="approverId: string&#xa;expenseId: string" success="notificationSent: boolean" error="NOTIFICATION_FAILED" id="Fx8VnH2m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="907" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="In-App&#xa;Notification&#xa;System" type="external_system" id="Kw5QmN9p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="1077" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Approver&#xa;Notified" type="event" id="Pz7TmC4x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Gm8PvX3c" vertex="1">
                        <mxGeometry x="1247" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge013" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Tv2YbM5q" target="Qp5VnR7x" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="220" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge014" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Wp7KvH9s" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge015" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Bx6NkL4r" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="560" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge016" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Nx8TmC2v" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="730" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge020" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Rw9HmP5t" target="Yx3KpL8v" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge021" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Yx3KpL8v" target="Fx8VnH2m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge022" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Fx8VnH2m" target="Kw5QmN9p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge023" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Gm8PvX3c" source="Kw5QmN9p" target="Pz7TmC4x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Dx5KmY8n" value="Manager Approval Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Bc7mP2wQ" vertex="1">
                    <mxGeometry x="34" y="1054" width="2200" height="350" as="geometry"/>
                </mxCell>
                <object label="Pending&#xa;Approvals View" type="read_model" id="Nt6VcQ2m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Approval&#xa;Interface" type="ui" id="Kw8PfX4s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="220" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Manager" type="actor" id="Hm9TnC5r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="390" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Review and&#xa;Approve/Reject" type="action" input="expenseId: string&#xa;decision: enum(approve,reject)&#xa;comments: string" success="reviewCompleted: boolean" error="REVIEW_FAILED" id="By7KpL9x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="560" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Manager&#xa;Approval&#xa;Policy" type="policy" id="Vx2QmH8t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="730" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="31" value="" style="edgeStyle=none;html=1;" edge="1" parent="Dx5KmY8n" source="Ly4NpW6m" target="Qw6TnH3m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="Manager&#xa;Approval Given" type="event" id="Ly4NpW6m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="900" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Rejection&#xa;Handling Policy" type="reaction_policy" id="Qw6TnH3m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="1070" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Notify Employee&#xa;of Rejection" type="action" input="employeeId: string&#xa;expenseId: string&#xa;feedback: string" success="notificationSent: boolean" error="NOTIFICATION_FAILED" id="Rx5VcL9k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="1240" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Notification&#xa;System" type="external_system" id="Zx8PmW4v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="1410" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Rejected" type="event" id="Mw7QkN5p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Dx5KmY8n" vertex="1">
                        <mxGeometry x="1580" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge025" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Nt6VcQ2m" target="Kw8PfX4s" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge026" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Kw8PfX4s" target="Hm9TnC5r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge027" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Hm9TnC5r" target="By7KpL9x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge028" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="By7KpL9x" target="Vx2QmH8t" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge029" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Vx2QmH8t" target="Ly4NpW6m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge031" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Qw6TnH3m" target="Rx5VcL9k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge032" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Rx5VcL9k" target="Zx8PmW4v" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge033" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Dx5KmY8n" source="Zx8PmW4v" target="Mw7QkN5p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Hy3VnM6c" value="Finance Approval Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Bc7mP2wQ" vertex="1">
                    <mxGeometry x="34" y="1434" width="2000" height="350" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="" style="edgeStyle=none;html=1;" edge="1" parent="Hy3VnM6c" source="Tx9FmB9p" target="Wx4PqN8m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="Finance&#xa;Approval Queue" type="read_model" id="Tx9FmB9p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="3" value="" style="edgeStyle=none;html=1;" edge="1" parent="Hy3VnM6c" source="Wx4PqN8m" target="Ry3VnH9t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="Finance&#xa;Approval UI" type="ui" id="Wx4PqN8m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="220" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" edge="1" parent="Hy3VnM6c" source="Ry3VnH9t" target="Lz5QkM4c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="Finance Team" type="actor" id="Ry3VnH9t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="390" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Final Approve&#xa;Expense" type="action" input="expenseId: string&#xa;decision: enum(approve,reject)&#xa;paymentDetails: object" success="approvalCompleted: boolean" error="APPROVAL_FAILED" id="Lz5QkM4c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="560" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Finance&#xa;Approval&#xa;Policy" type="policy" id="Fx7PmW6k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="730" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Finance&#xa;Approval Given" type="event" id="Qx6NkB2r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Hy3VnM6c" vertex="1">
                        <mxGeometry x="900" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge039" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Hy3VnM6c" source="Lz5QkM4c" target="Fx7PmW6k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge040" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Hy3VnM6c" source="Fx7PmW6k" target="Qx6NkB2r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Cx7PmV5k" value="CEO Approval Flow (Over Budget / High Amount)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Bc7mP2wQ" vertex="1">
                    <mxGeometry x="2350" y="810" width="2100" height="730" as="geometry"/>
                </mxCell>
                <object label="CEO Escalation&#xa;Policy" type="reaction_policy" id="Jy8VmN5c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="54" y="324" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Escalate to CEO" type="action" input="expenseId: string&#xa;escalationReason: string&#xa;budgetExceeded: boolean" success="escalationCreated: boolean" error="ESCALATION_FAILED" id="Tz3HqM8p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="224" y="324" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="High Amount&#xa;Escalation Policy" type="policy" id="Px5WkB3r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="394" y="324" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO Approval&#xa;Requested" type="event" id="Fz6NmT7v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="564" y="324" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO Approval&#xa;Dashboard" type="read_model" id="Qx4KpL9m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="54" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO Approval&#xa;Interface" type="ui" id="Wy7VnH6t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="224" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO" type="actor" id="Nx2TmC7k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="394" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Fast-Track&#xa;Approve" type="action" input="expenseId: string&#xa;decision: enum(approve,reject)&#xa;justification: string" success="finalApproval: boolean" error="APPROVAL_FAILED" id="Rx8PqW4m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="564" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO Fast-Track&#xa;Policy" type="policy" id="Kv4NmH8x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="734" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="CEO Final&#xa;Approval Given" type="event" id="Ty9FmB6p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="904" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Update Budget&#xa;Policy" type="reaction_policy" id="Lx3VnH9m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="1074" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Update Department&#xa;Budget" type="action" input="departmentId: string&#xa;amount: number&#xa;expenseId: string" success="budgetUpdated: boolean" error="UPDATE_FAILED" id="Mx6QkN2v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="1244" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Budget&#xa;Tracking Policy" type="policy" id="Ry5TmH4x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="1414" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Department&#xa;Budget Updated" type="event" id="Nz8VmL5k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Cx7PmV5k" vertex="1">
                        <mxGeometry x="1584" y="524" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge041" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Jy8VmN5c" target="Tz3HqM8p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge042" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Tz3HqM8p" target="Px5WkB3r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge043" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Px5WkB3r" target="Fz6NmT7v" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge044" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Fz6NmT7v" target="Qx4KpL9m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge045" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Qx4KpL9m" target="Wy7VnH6t" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge046" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Wy7VnH6t" target="Nx2TmC7k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge047" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Nx2TmC7k" target="Rx8PqW4m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge048" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Rx8PqW4m" target="Kv4NmH8x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge049" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Kv4NmH8x" target="Ty9FmB6p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge050" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Ty9FmB6p" target="Lx3VnH9m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge051" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Lx3VnH9m" target="Mx6QkN2v" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge052" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Mx6QkN2v" target="Ry5TmH4x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge053" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Cx7PmV5k" source="Ry5TmH4x" target="Nz8VmL5k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge012" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="Bc7mP2wQ" source="Kf5WbN2t" target="Tv2YbM5q" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge024" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="Bc7mP2wQ" source="Pz7TmC4x" target="Nt6VcQ2m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge035" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;dashed=1;" parent="Bc7mP2wQ" source="Ly4NpW6m" target="Jy8VmN5c" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="994" y="1328"/>
                            <mxPoint x="2464" y="1328"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <object label="Expense&#xa;Dashboard View" type="read_model" id="5">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Bc7mP2wQ">
                        <mxGeometry x="62" y="327" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Expense Request&#xa;Form" type="ui" id="6">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Bc7mP2wQ">
                        <mxGeometry x="232" y="327" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Employee" type="actor" id="7">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Bc7mP2wQ">
                        <mxGeometry x="402" y="327" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Create Expense&#xa;Request" type="action" input="amount: number&#xa;category: string&#xa;receipt: file&#xa;description: string&#xa;projectId: string" success="requestId: string" error="VALIDATION_ERROR&#xa;FILE_TOO_LARGE&#xa;INVALID_PROJECT" id="8">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Bc7mP2wQ">
                        <mxGeometry x="572" y="327" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="17" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="5" target="6" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="6" target="7" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="7" target="8" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="8" target="11" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="742" y="387" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" target="10" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="862" y="387" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="12" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1422" y="387" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="13" parent="Bc7mP2wQ">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1592" y="387" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0;entryDx=90;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;curved=1;" edge="1" parent="Bc7mP2wQ" source="16" target="Tv2YbM5q">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=orthogonalEdgeStyle;html=1;curved=1;" edge="1" parent="Bc7mP2wQ" source="Qx6NkB2r" target="Lx3VnH9m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Hx6MpQ3v" value="Budget Management" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="1700" width="2800" height="500" as="geometry"/>
                </mxCell>
                <mxCell id="Zx4NmP8k" value="Budget Definition &amp; Management Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Hx6MpQ3v" vertex="1">
                    <mxGeometry x="30" y="50" width="2700" height="400" as="geometry"/>
                </mxCell>
                <object label="Budget&#xa;Dashboard" type="read_model" id="Vw8TnL6m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Budget&#xa;Management UI" type="ui" id="Mx3VcH9r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="220" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Finance / CEO" type="actor" id="Ry5QkN3x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="390" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Define Department&#xa;Budget" type="action" input="departmentId: string&#xa;amount: number&#xa;period: date&#xa;projectAllocations: array" success="budgetId: string" error="VALIDATION_ERROR&#xa;BUDGET_EXISTS" id="Lx7PmW3t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="560" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Budget&#xa;Allocation Policy" type="policy" id="Fx6NkB5m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="730" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Department&#xa;Budget Defined" type="event" id="Qy8VmH6p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="900" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Budget Change&#xa;Impact Policy" type="reaction_policy" id="Nz3TmC8v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="1070" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Recalculate&#xa;Pending Requests" type="action" input="departmentId: string&#xa;newBudget: number&#xa;effectiveDate: date" success="recalculated: boolean" error="RECALCULATION_FAILED" id="Kx4PqL9n">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="1240" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Approval Route&#xa;Reassignment&#xa;Policy" type="policy" id="Wx7VnH3k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="1410" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Pending Requests&#xa;Recalculated" type="event" id="Py6TmH2x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Zx4NmP8k" vertex="1">
                        <mxGeometry x="1580" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge054" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Vw8TnL6m" target="Mx3VcH9r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge055" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Mx3VcH9r" target="Ry5QkN3x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge056" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Ry5QkN3x" target="Lx7PmW3t" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge057" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Lx7PmW3t" target="Fx6NkB5m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge058" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Fx6NkB5m" target="Qy8VmH6p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge059" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Qy8VmH6p" target="Nz3TmC8v" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge060" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Nz3TmC8v" target="Kx4PqL9n" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge061" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Kx4PqL9n" target="Wx7VnH3k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge062" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Zx4NmP8k" source="Wx7VnH3k" target="Py6TmH2x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Dx8VnM6c" value="User &amp; Delegation Management" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="50" y="2250" width="2500" height="500" as="geometry"/>
                </mxCell>
                <mxCell id="Jw7QkP4m" value="User Delegation &amp; Deactivation Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Dx8VnM6c" vertex="1">
                    <mxGeometry x="30" y="50" width="2400" height="400" as="geometry"/>
                </mxCell>
                <object label="User Management&#xa;Dashboard" type="read_model" id="Sx3VcL3k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User&lt;br&gt;Management UI" type="ui" id="Tx9FmB7p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="220" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User" type="actor" id="Wx4RqN8m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="390" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Deactivation&#xa;Validation Policy" type="reaction_policy" id="Qx6NkB3r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="1070" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Deactivate User" type="action" input="userId: string&#xa;delegationRequired: boolean" success="userDeactivated: boolean" error="DELEGATION_REQUIRED&#xa;PENDING_TASKS" id="My8VnL5t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="560" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User Deactivation&#xa;Policy" type="policy" id="Nz4TmH8p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="730" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="52" style="edgeStyle=none;html=1;" edge="1" parent="Jw7QkP4m" source="Ky5QmP9x" target="Qx6NkB3r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="User&#xa;Deactivated" type="event" id="Ky5QmP9x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Jw7QkP4m" vertex="1">
                        <mxGeometry x="900" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge063" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" source="Sx3VcL3k" target="Tx9FmB7p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge064" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" source="Tx9FmB7p" target="Wx4RqN8m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge065" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" source="Wx4RqN8m" target="My8VnL5t" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="505" y="36" as="sourcePoint"/>
                        <mxPoint x="560" y="140" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge066" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" target="Lz5QkM5c" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="680" y="140" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge067" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" target="Fx7PmW7k" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="850" y="140" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge068" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" target="Qx6NkB3r" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1020" y="140" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge070" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" source="My8VnL5t" target="Nz4TmH8p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge071" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Jw7QkP4m" source="Nz4TmH8p" target="Ky5QmP9x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <object label="User Management&#xa;Dashboard" type="read_model" id="33">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="54" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User&lt;br&gt;Management UI" type="ui" id="34">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="224" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User" type="actor" id="35">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="394" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Configure&#xa;Delegation" type="action" input="userId: string&#xa;delegateUserId: string&#xa;startDate: date&#xa;endDate: date" success="delegationId: string" error="VALIDATION_ERROR&#xa;DELEGATE_NOT_FOUND" id="36">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="564" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Delegation&#xa;Policy" type="policy" id="37">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="734" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="User Delegation&#xa;Configured" type="event" id="38">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Jw7QkP4m">
                        <mxGeometry x="904" y="247" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="33" target="34" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="34" target="35" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="35" target="36" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="509" y="203" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="36" target="37" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" source="37" target="38" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" target="40" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1194" y="307" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" target="41" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1364" y="307" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" edge="1" target="42" parent="Jw7QkP4m">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1534" y="307" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="53" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="Jw7QkP4m" source="Qx6NkB3r" target="36">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Ax9VnM7c" value="Audit Trail &amp; Compliance" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="1" vertex="1">
                    <mxGeometry x="5263" y="66" width="2200" height="500" as="geometry"/>
                </mxCell>
                <mxCell id="Bw8QkP5m" value="Audit Logging Flow" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" parent="Ax9VnM7c" vertex="1">
                    <mxGeometry x="30" y="50" width="2100" height="400" as="geometry"/>
                </mxCell>
                <object label="Audit Logging&#xa;Policy" type="reaction_policy" id="Rx3VcN7m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="50" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Log Action" type="action" input="userId: string&#xa;actionType: string&#xa;entityId: string&#xa;changes: object&#xa;timestamp: date" success="auditLogId: string" error="LOGGING_FAILED" id="Wx7PmB6k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="220" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Immutable&#xa;Audit Log Policy" type="policy" id="Lz8VnM5t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="390" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Audit Entry&#xa;Created" type="event" id="Fy6QkN8p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="560" y="80" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Audit Trail&#xa;View" type="read_model" id="Qx5TmH9v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="213" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Audit Trail UI" type="ui" id="Ny8VmL4k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="383" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Finance / CEO" type="actor" id="Kz3PmW6x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="553" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="View Audit Trail" type="action" input="entityId: string&#xa;entityType: string" success="auditHistory: array" error="ACCESS_DENIED" id="Mx4QkN7r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="723" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="2-Year Retention&#xa;Policy" type="policy" id="Ry5TmH5x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="893" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <object label="Audit Trail&#xa;Accessed" type="event" id="Nz8VmL6k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" parent="Bw8QkP5m" vertex="1">
                        <mxGeometry x="1063" y="262" width="120" height="120" as="geometry"/>
                    </mxCell>
                </object>
                <mxCell id="edge072" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Rx3VcN7m" target="Wx7PmB6k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge073" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Wx7PmB6k" target="Lz8VnM5t" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge074" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Lz8VnM5t" target="Fy6QkN8p" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge076" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Qx5TmH9v" target="Ny8VmL4k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge077" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Ny8VmL4k" target="Kz3PmW6x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge078" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Kz3PmW6x" target="Mx4QkN7r" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge079" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Mx4QkN7r" target="Ry5TmH5x" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge080" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" parent="Bw8QkP5m" source="Ry5TmH5x" target="Nz8VmL6k" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge081" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;dashed=1;" parent="1" source="Kf5WbN2t" target="Rx3VcN7m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge082" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;dashed=1;exitX=0;exitY=0;exitDx=45.00000000000001;exitDy=0;exitPerimeter=0;" parent="1" source="Ly4NpW6m" target="Rx3VcN7m" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1025" y="794"/>
                            <mxPoint x="5403" y="794"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge083" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;dashed=1;" parent="1" source="Qx6NkB2r" target="Rx3VcN7m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge084" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;dashed=1;" parent="1" source="Ty9FmB6p" target="Rx3VcN7m" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>