<mxfile host="65bd71144e">
    <diagram name="Expense Management System" id="ExpenseMgmtSystem">
        <mxGraphModel dx="2500" dy="1500" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="1169" pageHeight="827" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>

                <!-- Bounded Context: Expense Management -->
                <mxCell id="Kx7mP2wQ" value="Expense Management" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontSize=18;fontColor=#4D4D4D;glass=0;shadow=1;" vertex="1" parent="1">
                    <mxGeometry x="50" y="50" width="6800" height="2800" as="geometry"/>
                </mxCell>

                <!-- Flow 1: Expense Submission & Receipt Capture -->
                <mxCell id="Fn9BtYz3" value="Flow 1: Expense Submission &amp; Receipt Capture" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="50" width="2600" height="600" as="geometry"/>
                </mxCell>

                <!-- Read Model: Available Budget & Categories -->
                <object label="Available Budget &amp; Categories" type="read_model" entity="departmentBudget: Budget&#xa;expenseCategories: Category[]&#xa;projectCodes: Project[]&#xa;userProfile: UserProfile" id="Rd4VcH8s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="50" y="100" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Expense Request Form -->
                <object label="Expense Request Form" type="ui" id="Hm2Wn9Qk">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="280" y="100" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Employee -->
                <object label="Employee" type="actor" id="Tp8FxK3r">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="480" y="100" width="140" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Capture Receipt -->
                <object label="Capture Receipt" type="action" input="receiptImage: File&#xa;receiptType: string" success="receiptId: string&#xa;imageUrl: string" error="FILE_TOO_LARGE&#xa;INVALID_FORMAT&#xa;UPLOAD_FAILED" id="Vm5PnL7t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="670" y="80" width="180" height="160" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: OCR Service -->
                <object label="OCR Service" type="external_system" id="Qw9XtB2m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="900" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Receipt Captured -->
                <object label="Receipt Captured" type="event" id="Zn6DkY4p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1100" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Extract Receipt Data -->
                <object label="Extract Receipt Data" type="reaction_policy" id="Bm8TqR5w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1300" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Data Extracted -->
                <object label="Data Extracted" type="event" id="Ys3Jm6Fv">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1500" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Submit Expense Request -->
                <object label="Submit Expense Request" type="action" input="amount: number&#xa;category: string&#xa;date: Date&#xa;purpose: string&#xa;receiptId: string&#xa;department: string&#xa;projectCode: string" success="requestId: string&#xa;workflowId: string" error="INVALID_AMOUNT&#xa;MISSING_RECEIPT&#xa;INVALID_DATE&#xa;VALIDATION_ERROR" id="Cn7WvH9k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="670" y="320" width="200" height="200" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Request Validation Policy -->
                <object label="Request Validation Policy" type="policy" id="Gp4Lb8Xr">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="920" y="320" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Expense Request Submitted -->
                <object label="Expense Request Submitted" type="event" id="Jt2Nm5Rk">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1150" y="320" width="180" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Document Storage -->
                <object label="Document Storage" type="external_system" id="Wt9Sk3Lp">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1380" y="320" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Receipt Stored -->
                <object label="Receipt Stored" type="event" id="Pq6Hv9Dm">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1590" y="320" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Notification Service -->
                <object label="Notification Service" type="external_system" id="Xm4Yn8Bw">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1790" y="320" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Confirmation Sent -->
                <object label="Confirmation Sent" type="event" id="Km7Zt5Pn">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="2010" y="320" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 1 -->
                <mxCell id="edgeRd4VcH8s" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rd4VcH8s" target="Hm2Wn9Qk" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeHm2Wn9Qk" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hm2Wn9Qk" target="Tp8FxK3r" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeTp8FxK3r" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tp8FxK3r" target="Vm5PnL7t" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeVm5PnL7t" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vm5PnL7t" target="Qw9XtB2m" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQw9XtB2m" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Qw9XtB2m" target="Zn6DkY4p" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeZn6DkY4p" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zn6DkY4p" target="Bm8TqR5w" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeBm8TqR5w" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bm8TqR5w" target="Ys3Jm6Fv" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeYs3JmToCn" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" source="Ys3Jm6Fv" target="Cn7WvH9k" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeCn7WvH9k" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cn7WvH9k" target="Gp4Lb8Xr" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeGp4Lb8Xr" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gp4Lb8Xr" target="Jt2Nm5Rk" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeJt2Nm5Rk" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jt2Nm5Rk" target="Wt9Sk3Lp" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeWt9Sk3Lp" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wt9Sk3Lp" target="Pq6Hv9Dm" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgePq6Hv9Dm" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pq6Hv9Dm" target="Xm4Yn8Bw" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeXm4Yn8Bw" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xm4Yn8Bw" target="Km7Zt5Pn" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 2: Budget Validation & Auto-Approval -->
                <mxCell id="Lm3Kp7Qt" value="Flow 2: Budget Validation &amp; Auto-Approval" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="700" width="2600" height="550" as="geometry"/>
                </mxCell>

                <!-- Reaction Policy: Validate Budget -->
                <object label="Validate Budget" type="reaction_policy" id="Np5Qx8Tk">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="50" y="80" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Budget Validation Policy -->
                <object label="Budget Validation Policy" type="policy" id="Rv6Zw9Xm">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="260" y="80" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Budget Validated -->
                <object label="Budget Validated" type="event" id="Sw7Fy4Bn">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="490" y="80" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Check Auto-Approval -->
                <object label="Check Auto-Approval Eligibility" type="reaction_policy" id="Tv8Gm3Rp">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="700" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Auto-Approval Policy -->
                <object label="Auto-Approval Policy&#xa;(Amount &lt; 20M VND)" type="policy" id="Um9Hp6Wq">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="930" y="80" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Request Auto-Approved -->
                <object label="Request Auto-Approved" type="event" id="Vn2Jt7Ks">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="1160" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Route to Finance -->
                <object label="Route to Finance for Payment" type="reaction_policy" id="Wm4Kp9Lx">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="1380" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Notification Service (Auto-Approval) -->
                <object label="Notification Service" type="external_system" id="Xm5Zq8Nr">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="1610" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Auto-Approval Notification Sent -->
                <object label="Auto-Approval Notification Sent" type="event" id="Yn6Bm7Wt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="1830" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Route to Manual Approval -->
                <object label="Route to Manual Approval Workflow" type="reaction_policy" id="Zm7Ct4Px">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="700" y="320" width="200" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Workflow Routing Policy -->
                <object label="Workflow Routing Policy" type="policy" id="An8Dm5Qv">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="950" y="320" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Routed to Manager -->
                <object label="Routed to Manager" type="event" id="Bn9Em6Rt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Lm3Kp7Qt">
                        <mxGeometry x="1180" y="320" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 2 -->
                <mxCell id="edgeJt2toNp5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" source="Jt2Nm5Rk" target="Np5Qx8Tk" edge="1" parent="Kx7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeNp5Qx8Tk" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Np5Qx8Tk" target="Rv6Zw9Xm" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeRv6Zw9Xm" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rv6Zw9Xm" target="Sw7Fy4Bn" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeSw7Fy4Bn" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sw7Fy4Bn" target="Tv8Gm3Rp" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeTv8Gm3Rp1" value="if &lt; 20M &amp; within budget" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tv8Gm3Rp" target="Um9Hp6Wq" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeTv8Gm3Rp2" value="else" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" source="Tv8Gm3Rp" target="Zm7Ct4Px" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeUm9Hp6Wq" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Um9Hp6Wq" target="Vn2Jt7Ks" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeVn2Jt7Ks" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vn2Jt7Ks" target="Wm4Kp9Lx" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeWm4Kp9Lx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wm4Kp9Lx" target="Xm5Zq8Nr" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeXm5Zq8Nr" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xm5Zq8Nr" target="Yn6Bm7Wt" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeZm7Ct4Px" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zm7Ct4Px" target="An8Dm5Qv" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeAn8Dm5Qv" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="An8Dm5Qv" target="Bn9Em6Rt" edge="1" parent="Lm3Kp7Qt">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 3: Manager Approval Process -->
                <mxCell id="Cp4Xm9Lv" value="Flow 3: Manager Approval Process" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="1300" width="2600" height="650" as="geometry"/>
                </mxCell>

                <!-- Read Model: Pending Approvals -->
                <object label="Pending Approvals Queue" type="read_model" entity="pendingRequests: Request[]&#xa;departmentBudget: Budget&#xa;requestHistory: History[]" id="Dn5Wt8Kp">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="50" y="100" width="190" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Approval Dashboard -->
                <object label="Approval Dashboard" type="ui" id="En6Xq9Rm">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="290" y="100" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Manager -->
                <object label="Manager" type="actor" id="Fn7Ym4Qt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="500" y="100" width="140" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Approve Request -->
                <object label="Approve Request" type="action" input="requestId: string&#xa;comments: string&#xa;approverId: string" success="approvalId: string&#xa;nextStage: string" error="INVALID_REQUEST&#xa;BUDGET_EXCEEDED&#xa;UNAUTHORIZED" id="Gm8Zp5Wt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="690" y="80" width="180" height="160" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Manager Approval Policy -->
                <object label="Manager Approval Policy" type="policy" id="Hn9Aq6Xr">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="920" y="80" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Manager Approved -->
                <object label="Manager Approved" type="event" id="Jp2Br7Ks">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1150" y="80" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Route to Finance -->
                <object label="Route to Finance Review" type="reaction_policy" id="Kn3Cs8Tm">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1360" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Routed to Finance -->
                <object label="Routed to Finance" type="event" id="Lm4Dt9Un">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1590" y="80" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Reject Request -->
                <object label="Reject Request" type="action" input="requestId: string&#xa;reason: string&#xa;approverId: string" success="rejectionId: string" error="INVALID_REQUEST&#xa;UNAUTHORIZED" id="Mn5Eq6Vp">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="690" y="340" width="180" height="160" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Rejection Policy -->
                <object label="Rejection Policy" type="policy" id="Nn6Fr7Wq">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="920" y="340" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Request Rejected -->
                <object label="Request Rejected" type="event" id="On7Gs8Xr">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1130" y="340" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Notification Service (Rejection) -->
                <object label="Notification Service" type="external_system" id="Pn8Ht9Ys">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1340" y="340" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Rejection Notification Sent -->
                <object label="Rejection Notification Sent" type="event" id="Qm9Ju6Zt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Cp4Xm9Lv">
                        <mxGeometry x="1560" y="340" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 3 -->
                <mxCell id="edgeBn9toEn6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" source="Bn9Em6Rt" target="Dn5Wt8Kp" edge="1" parent="Kx7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeDn5Wt8Kp" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dn5Wt8Kp" target="En6Xq9Rm" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeEn6Xq9Rm" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="En6Xq9Rm" target="Fn7Ym4Qt" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeFn7Ym4Qt1" value="approve" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;" source="Fn7Ym4Qt" target="Gm8Zp5Wt" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeFn7Ym4Qt2" value="reject" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" source="Fn7Ym4Qt" target="Mn5Eq6Vp" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeGm8Zp5Wt" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gm8Zp5Wt" target="Hn9Aq6Xr" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeHn9Aq6Xr" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hn9Aq6Xr" target="Jp2Br7Ks" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeJp2Br7Ks" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jp2Br7Ks" target="Kn3Cs8Tm" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeKn3Cs8Tm" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Kn3Cs8Tm" target="Lm4Dt9Un" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeMn5Eq6Vp" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mn5Eq6Vp" target="Nn6Fr7Wq" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeNn6Fr7Wq" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Nn6Fr7Wq" target="On7Gs8Xr" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeOn7Gs8Xr" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="On7Gs8Xr" target="Pn8Ht9Ys" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgePn8Ht9Ys" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pn8Ht9Ys" target="Qm9Ju6Zt" edge="1" parent="Cp4Xm9Lv">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 4: Finance Approval & CEO Escalation -->
                <mxCell id="Rn2Bv7Ks" value="Flow 4: Finance Approval &amp; CEO Escalation" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="2700" y="50" width="4050" height="900" as="geometry"/>
                </mxCell>

                <!-- Read Model: Finance Queue -->
                <object label="Finance Approval Queue" type="read_model" entity="pendingFinanceReviews: Request[]&#xa;organizationBudget: Budget&#xa;complianceData: Compliance[]" id="Sn3Cw8Xm">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="50" y="100" width="200" height="150" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Finance Dashboard -->
                <object label="Finance Dashboard" type="ui" id="Tn4Dx9Yn">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="300" y="100" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Finance -->
                <object label="Finance" type="actor" id="Un5Ey7Zp">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="510" y="100" width="140" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Review & Approve -->
                <object label="Review &amp; Approve" type="action" input="requestId: string&#xa;comments: string&#xa;complianceCheck: boolean" success="approvalId: string&#xa;paymentQueued: boolean" error="COMPLIANCE_ISSUE&#xa;BUDGET_EXCEEDED&#xa;UNAUTHORIZED" id="Vn6Fz8Aq">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="700" y="80" width="200" height="170" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Finance Approval Policy -->
                <object label="Finance Approval Policy" type="policy" id="Wn7Gw9Br">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="950" y="80" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Finance Approved -->
                <object label="Finance Approved" type="event" id="Xn8Hx6Cs">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1180" y="80" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Check CEO Requirement -->
                <object label="Check CEO Escalation Rule" type="reaction_policy" id="Yn9Iy7Dt">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1390" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: CEO Escalation Policy -->
                <object label="CEO Escalation Policy&#xa;(Amount &gt; 20M OR over budget)" type="policy" id="Zn4Jz8Eu">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1620" y="80" width="210" height="150" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Routed to CEO -->
                <object label="Routed to CEO" type="event" id="An5Kw9Fv">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1880" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: CEO Dashboard -->
                <object label="CEO Approval Queue" type="read_model" entity="escalatedRequests: Request[]&#xa;organizationMetrics: Metrics" id="Bn6Lx4Gw">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2080" y="80" width="190" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: CEO Dashboard -->
                <object label="CEO Dashboard" type="ui" id="Cn7My5Hx">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2320" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: CEO -->
                <object label="CEO" type="actor" id="Dn8Nz6Iy">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2520" y="80" width="130" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: CEO Approve -->
                <object label="CEO Approve" type="action" input="requestId: string&#xa;overrideReason: string" success="approvalId: string&#xa;finalApproved: boolean" error="INVALID_REQUEST&#xa;UNAUTHORIZED" id="En9Ow7Jz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2700" y="80" width="190" height="160" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: CEO Override Policy -->
                <object label="CEO Override Policy" type="policy" id="Fn4Px8Kw">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2940" y="80" width="180" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: CEO Approved -->
                <object label="CEO Approved" type="event" id="Gn5Qy9Lx">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="3170" y="80" width="150" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Request Fully Approved -->
                <object label="Request Fully Approved" type="event" id="Hn6Rz4My">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="3370" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Notification Service (Final) -->
                <object label="Notification Service" type="external_system" id="In7Sw5Nz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="3600" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Final Approval Notification -->
                <object label="Final Approval Notification Sent" type="event" id="Jn8Tx6Oz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="3820" y="80" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Complete Without CEO -->
                <object label="Complete Approval&#xa;(No CEO needed)" type="reaction_policy" id="Kn9Uy7Pz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1390" y="350" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Approval Complete (No CEO) -->
                <object label="Approval Complete" type="event" id="Ln4Vz8Qw">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1620" y="350" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Queue for Payment -->
                <object label="Queue for Payment Processing" type="reaction_policy" id="Mn5Ww9Rx">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="1840" y="350" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Queued for Payment -->
                <object label="Queued for Payment" type="event" id="Nn6Xx4Sy">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Rn2Bv7Ks">
                        <mxGeometry x="2080" y="350" width="180" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 4 -->
                <mxCell id="edgeLm4toSn3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" source="Lm4Dt9Un" target="Sn3Cw8Xm" edge="1" parent="Kx7mP2wQ">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeSn3Cw8Xm" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sn3Cw8Xm" target="Tn4Dx9Yn" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeTn4Dx9Yn" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tn4Dx9Yn" target="Un5Ey7Zp" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeUn5Ey7Zp" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Un5Ey7Zp" target="Vn6Fz8Aq" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeVn6Fz8Aq" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vn6Fz8Aq" target="Wn7Gw9Br" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeWn7Gw9Br" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wn7Gw9Br" target="Xn8Hx6Cs" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeXn8Hx6Cs" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xn8Hx6Cs" target="Yn9Iy7Dt" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeYn9Iy7Dt1" value="if &gt; 20M OR over budget" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yn9Iy7Dt" target="Zn4Jz8Eu" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeYn9Iy7Dt2" value="else complete" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" source="Yn9Iy7Dt" target="Kn9Uy7Pz" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeZn4Jz8Eu" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zn4Jz8Eu" target="An5Kw9Fv" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeAn5Kw9Fv" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="An5Kw9Fv" target="Bn6Lx4Gw" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeBn6Lx4Gw" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bn6Lx4Gw" target="Cn7My5Hx" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeCn7My5Hx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cn7My5Hx" target="Dn8Nz6Iy" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeDn8Nz6Iy" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dn8Nz6Iy" target="En9Ow7Jz" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeEn9Ow7Jz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="En9Ow7Jz" target="Fn4Px8Kw" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeFn4Px8Kw" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fn4Px8Kw" target="Gn5Qy9Lx" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeGn5Qy9Lx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gn5Qy9Lx" target="Hn6Rz4My" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeHn6Rz4My" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hn6Rz4My" target="In7Sw5Nz" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeIn7Sw5Nz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="In7Sw5Nz" target="Jn8Tx6Oz" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeKn9Uy7Pz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Kn9Uy7Pz" target="Ln4Vz8Qw" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeLn4Vz8Qw" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ln4Vz8Qw" target="Mn5Ww9Rx" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeMn5Ww9Rx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mn5Ww9Rx" target="Nn6Xx4Sy" edge="1" parent="Rn2Bv7Ks">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 5: CEO Direct Override -->
                <mxCell id="On3Yz5Tw" value="Flow 5: CEO Direct Override (Any Stage)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="2700" y="1000" width="2200" height="500" as="geometry"/>
                </mxCell>

                <!-- Read Model: All Requests -->
                <object label="Organization-Wide Requests" type="read_model" entity="allRequests: Request[]&#xa;organizationMetrics: Metrics" id="Pn4Aw6Ux">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="50" y="100" width="210" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: CEO Override Interface -->
                <object label="CEO Override Interface" type="ui" id="Qn5Bx7Vy">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="310" y="100" width="180" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: CEO (Override) -->
                <object label="CEO" type="actor" id="Rn6Cy8Wz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="540" y="100" width="130" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Override Approve -->
                <object label="Override Approve" type="action" input="requestId: string&#xa;overrideReason: string&#xa;bypassStages: string[]" success="approvalId: string&#xa;immediateComplete: boolean" error="MISSING_REASON&#xa;INVALID_REQUEST" id="Sn7Dz9Xx">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="720" y="80" width="200" height="170" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: CEO Direct Override Policy -->
                <object label="CEO Direct Override Policy" type="policy" id="Tn8Ew4Yy">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="970" y="80" width="200" height="150" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: CEO Override Approved -->
                <object label="CEO Override Approved" type="event" id="Un9Fv5Zz">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="1220" y="80" width="180" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Complete Immediately -->
                <object label="Complete Request Immediately" type="reaction_policy" id="Vn4Gw6Aa">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="1450" y="80" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Override Complete -->
                <object label="Override Approval Complete" type="event" id="Wn5Hx7Bb">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="1690" y="80" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Notification (Override) -->
                <object label="Notification Service" type="external_system" id="Xn6Iy8Cc">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="On3Yz5Tw">
                        <mxGeometry x="1930" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 5 -->
                <mxCell id="edgePn4Aw6Ux" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pn4Aw6Ux" target="Qn5Bx7Vy" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQn5Bx7Vy" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Qn5Bx7Vy" target="Rn6Cy8Wz" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeRn6Cy8Wz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rn6Cy8Wz" target="Sn7Dz9Xx" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeSn7Dz9Xx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sn7Dz9Xx" target="Tn8Ew4Yy" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeTn8Ew4Yy" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tn8Ew4Yy" target="Un9Fv5Zz" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeUn9Fv5Zz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Un9Fv5Zz" target="Vn4Gw6Aa" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeVn4Gw6Aa" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vn4Gw6Aa" target="Wn5Hx7Bb" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeWn5Hx7Bb" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wn5Hx7Bb" target="Xn6Iy8Cc" edge="1" parent="On3Yz5Tw">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 6: Receipt Archive & Retrieval -->
                <mxCell id="Yn7Jz9Dd" value="Flow 6: Receipt Management &amp; Archive" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FFFFFF;gradientColor=none;swimlaneFillColor=#FAFAFA;fontStyle=1;fontSize=14;fontColor=#333333;glass=0;shadow=1;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="2700" y="1550" width="2100" height="500" as="geometry"/>
                </mxCell>

                <!-- Read Model: Receipt Archive -->
                <object label="Receipt Archive" type="read_model" entity="storedReceipts: Receipt[]&#xa;metadata: DocumentMetadata[]&#xa;searchIndex: SearchIndex" id="Zn8Kw5Ee">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="50" y="100" width="200" height="150" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Document Search Interface -->
                <object label="Document Search Interface" type="ui" id="An9Lx6Ff">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="300" y="100" width="190" height="130" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Finance/Manager -->
                <object label="Finance / Manager" type="actor" id="Bn4My7Gg">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="540" y="100" width="160" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Search Receipts -->
                <object label="Search Receipts" type="action" input="searchCriteria: SearchCriteria&#xa;dateRange: DateRange&#xa;amountRange: AmountRange" success="results: Receipt[]&#xa;totalCount: number" error="INVALID_CRITERIA&#xa;SEARCH_ERROR" id="Cn5Nz8Hh">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="750" y="80" width="210" height="170" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Document Storage (Retrieval) -->
                <object label="Document Storage" type="external_system" id="Dn6Ow9Ii">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="1010" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Receipts Retrieved -->
                <object label="Receipts Retrieved" type="event" id="En7Px4Jj">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="1230" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Download Receipt -->
                <object label="Download Receipt" type="action" input="receiptId: string&#xa;format: string" success="downloadUrl: string&#xa;expiresAt: DateTime" error="NOT_FOUND&#xa;ACCESS_DENIED" id="Fn8Qy5Kk">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=left;verticalAlign=top;spacing=10;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="1450" y="80" width="190" height="160" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Receipt Downloaded -->
                <object label="Receipt Downloaded" type="event" id="Gn9Rz6Ll">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Yn7Jz9Dd">
                        <mxGeometry x="1690" y="80" width="170" height="120" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 6 -->
                <mxCell id="edgeZn8Kw5Ee" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zn8Kw5Ee" target="An9Lx6Ff" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeAn9Lx6Ff" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="An9Lx6Ff" target="Bn4My7Gg" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeBn4My7Gg" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bn4My7Gg" target="Cn5Nz8Hh" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeCn5Nz8Hh" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cn5Nz8Hh" target="Dn6Ow9Ii" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeDn6Ow9Ii" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dn6Ow9Ii" target="En7Px4Jj" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeEn7Px4Jj" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="En7Px4Jj" target="Fn8Qy5Kk" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeFn8Qy5Kk" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fn8Qy5Kk" target="Gn9Rz6Ll" edge="1" parent="Yn7Jz9Dd">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
