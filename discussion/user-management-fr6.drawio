<mxfile host="65bd71144e">
    <diagram name="User Management - FR-6" id="UserMgmt-FR6">
        <mxGraphModel dx="1469" dy="936" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="1169" pageHeight="827" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>

                <!-- Bounded Context: User Management -->
                <mxCell id="Kx7mP2wQ" value="User Management" style="swimlane;whiteSpace=wrap;html=1;strokeColor=none;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#4D4D4D;glass=0;shadow=0;" vertex="1" parent="1">
                    <mxGeometry x="50" y="50" width="3600" height="1400" as="geometry">
                        <mxRectangle x="50" y="50" width="180" height="26" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>

                <!-- Flow 1: User Account Creation -->
                <mxCell id="Fn9BtYz3" value="Flow: User Account Creation (AC-6.2-1)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FAFAFA;gradientColor=none;swimlaneFillColor=#FFFFFF;fontStyle=1;fontColor=#333333;glass=0;shadow=0;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="60" width="1620" height="380" as="geometry"/>
                </mxCell>

                <!-- User Account Creation Flow Elements -->
                <object label="Active Users&amp;#xa;List" type="read_model" entity="userId: number&amp;#xa;email: string&amp;#xa;name: string&amp;#xa;role: string&amp;#xa;department: string&amp;#xa;status: string" id="Rd4VcH8s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="40" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User Management&amp;#xa;UI" type="ui" id="Mz5NqW7k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="220" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Admin User" type="actor" id="Bw2XtJ6p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="400" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Create User&amp;#xa;Account" type="action" input="email: string&amp;#xa;name: string&amp;#xa;role: enum&amp;#xa;department: string&amp;#xa;position: string" success="userId: number" error="VALIDATION_ERROR&amp;#xa;DUPLICATE_EMAIL&amp;#xa;INTERNAL_ERROR" id="Hy8QtP3m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="580" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User Management&amp;#xa;Policy" type="policy" id="Vn4LrD9x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="760" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User Account&amp;#xa;Created" type="event" id="Tz6McF2b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="940" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Send Welcome&amp;#xa;Email" type="reaction_policy" id="Pk7WsN5v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1120" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Send Welcome&amp;#xa;Email" type="action" input="userId: number&amp;#xa;email: string" success="emailSent: boolean" error="EMAIL_ERROR" id="Qm3BxK8t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1300" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Email System" type="external_system" id="Jv9ZnR4q">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1300" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Welcome Email&amp;#xa;Sent" type="event" id="Lw5YpT8h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Fn9BtYz3">
                        <mxGeometry x="1460" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 1 -->
                <mxCell id="edgeA1b2C3d4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rd4VcH8s" target="Mz5NqW7k" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeE5f6G7h8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mz5NqW7k" target="Bw2XtJ6p" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeI9j0K1l2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bw2XtJ6p" target="Hy8QtP3m" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeM3n4O5p6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hy8QtP3m" target="Vn4LrD9x" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQ7r8S9t0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vn4LrD9x" target="Tz6McF2b" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeU1v2W3x4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tz6McF2b" target="Pk7WsN5v" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeY5z6A7b8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pk7WsN5v" target="Qm3BxK8t" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeC9d0E1f2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Qm3BxK8t" target="Jv9ZnR4q" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeG3h4I5j6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jv9ZnR4q" target="Lw5YpT8h" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 2: Role Assignment -->
                <mxCell id="Zx8DnM2v" value="Flow: Role Assignment &amp; Permissions (AC-6.1-1, AC-6.1-2)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FAFAFA;gradientColor=none;swimlaneFillColor=#FFFFFF;fontStyle=1;fontColor=#333333;glass=0;shadow=0;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="480" width="1440" height="380" as="geometry"/>
                </mxCell>

                <!-- Role Assignment Flow Elements -->
                <object label="User Profile&amp;#xa;Data" type="read_model" entity="userId: number&amp;#xa;currentRole: string&amp;#xa;department: string&amp;#xa;permissions: array" id="Ns6TvH9k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="40" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Role Management&amp;#xa;UI" type="ui" id="Xp4WqL7m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="220" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Admin/CEO" type="actor" id="Cv8ZrK3n">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="400" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Assign Role" type="action" input="userId: number&amp;#xa;newRole: enum&amp;#xa;reason: string" success="roleUpdated: boolean" error="UNAUTHORIZED&amp;#xa;INVALID_ROLE&amp;#xa;VALIDATION_ERROR" id="Dm5BsP6t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="580" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Role Assignment&amp;#xa;Policy" type="policy" id="Fw9HtQ2x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="760" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Role Assigned" type="event" id="Gw3KvN8y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="940" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Update User&amp;#xa;Permissions" type="reaction_policy" id="Jx7LwM4z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="1120" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Permissions&amp;#xa;Updated" type="event" id="Ky2NxP5w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Zx8DnM2v">
                        <mxGeometry x="1280" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 2 -->
                <mxCell id="edgeK7l8M9n0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ns6TvH9k" target="Xp4WqL7m" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeO1p2Q3r4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xp4WqL7m" target="Cv8ZrK3n" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeS5t6U7v8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cv8ZrK3n" target="Dm5BsP6t" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeW9x0Y1z2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dm5BsP6t" target="Fw9HtQ2x" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeA3b4C5d6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fw9HtQ2x" target="Gw3KvN8y" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeE7f8G9h0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gw3KvN8y" target="Jx7LwM4z" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeI1j2K3l4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jx7LwM4z" target="Ky2NxP5w" edge="1" parent="Zx8DnM2v">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 3: Authentication (AC-6.4-1, AC-6.4-2, AC-6.4-3) -->
                <mxCell id="Pv7QwX4y" value="Flow: Authentication &amp; Session Management (AC-6.4-1, AC-6.4-2, AC-6.4-3)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FAFAFA;gradientColor=none;swimlaneFillColor=#FFFFFF;fontStyle=1;fontColor=#333333;glass=0;shadow=0;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="1700" y="60" width="1840" height="380" as="geometry"/>
                </mxCell>

                <!-- Authentication Flow Elements -->
                <object label="Login Form" type="read_model" entity="email: string&amp;#xa;password: string" id="Rt8YzB3w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="40" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Login UI" type="ui" id="Sv2DcE6x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="220" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User" type="actor" id="Tw6FgH9y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="400" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Authenticate&amp;#xa;User" type="action" input="email: string&amp;#xa;password: string" success="sessionId: string&amp;#xa;userId: number" error="INVALID_CREDENTIALS&amp;#xa;ACCOUNT_LOCKED&amp;#xa;INTERNAL_ERROR" id="Ux3JkL5z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="580" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Authentication&amp;#xa;Policy" type="policy" id="Vw7MnP2a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="760" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User&amp;#xa;Authenticated" type="event" id="Wx8QrT3b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="940" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Create Session" type="reaction_policy" id="Xy4SvU6c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1120" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Session Created" type="event" id="Yz9WxV7d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1280" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Authentication&amp;#xa;Failed" type="event" id="Za5YbW8e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="940" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Lockout Policy" type="reaction_policy" id="Ab6ZcX9f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1120" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Account Locked" type="event" id="Bc7AdY0g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1280" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Notify User" type="reaction_policy" id="Cd8BeZ1h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1460" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Lockout&amp;#xa;Notification Sent" type="event" id="De9CfA2i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Pv7QwX4y">
                        <mxGeometry x="1640" y="240" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 3 -->
                <mxCell id="edgeM5n6O7p8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rt8YzB3w" target="Sv2DcE6x" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQ9r0S1t2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sv2DcE6x" target="Tw6FgH9y" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeU3v4W5x6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tw6FgH9y" target="Ux3JkL5z" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeY7z8A9b0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ux3JkL5z" target="Vw7MnP2a" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeC1d2E3f4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vw7MnP2a" target="Wx8QrT3b" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeG5h6I7j8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wx8QrT3b" target="Xy4SvU6c" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeK9l0M1n2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xy4SvU6c" target="Yz9WxV7d" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeO3p4Q5r6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vw7MnP2a" target="Za5YbW8e" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeS7t8U9v0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Za5YbW8e" target="Ab6ZcX9f" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeW1x2Y3z4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ab6ZcX9f" target="Bc7AdY0g" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeA5b6C7d8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bc7AdY0g" target="Cd8BeZ1h" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeE9f0G1h2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cd8BeZ1h" target="De9CfA2i" edge="1" parent="Pv7QwX4y">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 4: Department Hierarchy (AC-6.3-1) -->
                <mxCell id="Qr4TsV7w" value="Flow: Department Hierarchy Assignment (AC-6.3-1)" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FAFAFA;gradientColor=none;swimlaneFillColor=#FFFFFF;fontStyle=1;fontColor=#333333;glass=0;shadow=0;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="1700" y="480" width="1440" height="380" as="geometry"/>
                </mxCell>

                <!-- Department Hierarchy Flow Elements -->
                <object label="Department&amp;#xa;Hierarchy View" type="read_model" entity="departmentId: number&amp;#xa;name: string&amp;#xa;parentDept: number&amp;#xa;deptHead: number&amp;#xa;members: array" id="Ef0GhI3j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="40" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Department&amp;#xa;Management UI" type="ui" id="Fg1HjJ4k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="220" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Admin User" type="actor" id="Gh2IkK5l">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="400" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Assign to&amp;#xa;Department" type="action" input="userId: number&amp;#xa;departmentId: number&amp;#xa;role: string" success="assignmentId: number" error="INVALID_DEPARTMENT&amp;#xa;USER_NOT_FOUND&amp;#xa;VALIDATION_ERROR" id="Hi3JlL6m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="580" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Department&amp;#xa;Assignment Policy" type="policy" id="Ij4KmM7n">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="760" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User Assigned&amp;#xa;to Department" type="event" id="Jk5LnN8o">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="940" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Update Approval&amp;#xa;Chain" type="reaction_policy" id="Kl6MoO9p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="1120" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Approval Chain&amp;#xa;Updated" type="event" id="Lm7NpP0q">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Qr4TsV7w">
                        <mxGeometry x="1280" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 4 -->
                <mxCell id="edgeI3j4K5l6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ef0GhI3j" target="Fg1HjJ4k" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeM7n8O9p0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fg1HjJ4k" target="Gh2IkK5l" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQ1r2S3t4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gh2IkK5l" target="Hi3JlL6m" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeU5v6W7x8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hi3JlL6m" target="Ij4KmM7n" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeY9z0A1b2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ij4KmM7n" target="Jk5LnN8o" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeC3d4E5f6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jk5LnN8o" target="Kl6MoO9p" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeG7h8I9j0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Kl6MoO9p" target="Lm7NpP0q" edge="1" parent="Qr4TsV7w">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Flow 5: User Deactivation with Delegation -->
                <mxCell id="Nr8UsW5x" value="Flow: User Deactivation with Delegation Enforcement" style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=1;fillColor=#FAFAFA;gradientColor=none;swimlaneFillColor=#FFFFFF;fontStyle=1;fontColor=#333333;glass=0;shadow=0;" vertex="1" parent="Kx7mP2wQ">
                    <mxGeometry x="30" y="900" width="1800" height="440" as="geometry"/>
                </mxCell>

                <!-- User Deactivation Flow Elements -->
                <object label="User Status&amp;#xa;View" type="read_model" entity="userId: number&amp;#xa;status: string&amp;#xa;pendingTasks: array&amp;#xa;delegateUserId: number" id="Os9VtX6y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#cdde6b;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="40" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User Management&amp;#xa;UI" type="ui" id="Pt0WuY7z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="220" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Admin User" type="actor" id="Qu1XvZ8a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="400" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Deactivate User" type="action" input="userId: number&amp;#xa;reason: string&amp;#xa;delegateUserId: number" success="deactivated: boolean" error="PENDING_TASKS&amp;#xa;NO_DELEGATE&amp;#xa;VALIDATION_ERROR" id="Rv2YwA9b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="580" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Delegation&amp;#xa;Enforcement Policy" type="policy" id="Sw3ZxB0c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="760" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Delegation&amp;#xa;Required" type="event" id="Tx4AyC1d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="760" y="260" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Prompt Delegation&amp;#xa;Configuration" type="reaction_policy" id="Uy5BzD2e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="940" y="260" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Delegation&amp;#xa;Configured" type="event" id="Vz6CaE3f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="940" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Transfer&amp;#xa;Responsibilities" type="reaction_policy" id="Wa7DbF4g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="1120" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Responsibilities&amp;#xa;Transferred" type="event" id="Xb8EcG5h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="1300" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="Update User&amp;#xa;Status" type="reaction_policy" id="Yc9FdH6i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="1480" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <object label="User&amp;#xa;Deactivated" type="event" id="Zd0GeI7j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;" vertex="1" parent="Nr8UsW5x">
                        <mxGeometry x="1640" y="80" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 5 -->
                <mxCell id="edgeK1l2M3n4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Os9VtX6y" target="Pt0WuY7z" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeO5p6Q7r8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pt0WuY7z" target="Qu1XvZ8a" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeS9t0U1v2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Qu1XvZ8a" target="Rv2YwA9b" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeW3x4Y5z6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rv2YwA9b" target="Sw3ZxB0c" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeA7b8C9d0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sw3ZxB0c" target="Tx4AyC1d" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeE1f2G3h4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Tx4AyC1d" target="Uy5BzD2e" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeI5j6K7l8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sw3ZxB0c" target="Vz6CaE3f" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeM9n0O1p2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vz6CaE3f" target="Wa7DbF4g" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeQ3r4S5t6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wa7DbF4g" target="Xb8EcG5h" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeU7v8W9x0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xb8EcG5h" target="Yc9FdH6i" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <mxCell id="edgeY1z2A3b4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yc9FdH6i" target="Zd0GeI7j" edge="1" parent="Nr8UsW5x">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

            </root>
        </mxGraphModel>
    </diagram>
</mxfile>