version: "3.9"

services:
  core-x-agent:
    image: quochuydev/core-x-agent:0.0.5
    container_name: core-x-agent
    ports:
      - "8079:8079"
    environment:
      - VERSION=0.0.5
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    env_file:
      - .env
    volumes:
      - ./agent.md:/app/agent.md
    depends_on:
      - neo4j

  neo4j:
    image: neo4j:latest
    container_name: neo4j
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4JLABS_PLUGINS=["apoc"]
      - NEO4J_PLUGINS=["apoc"]
    ports:
      - "7474:7474" # browser
      - "7687:7687" # bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs

volumes:
  neo4j_data:
  neo4j_logs:
