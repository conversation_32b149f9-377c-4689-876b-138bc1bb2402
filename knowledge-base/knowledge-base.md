### Run agent in local

```sh
# Down
docker compose down

# Create .env file
cp .env.example .env

# Run
docker compose up -d

# Check logs
docker compose logs core-x-agent -f

# Tools: `train_data` `search_knowledge_base`

# Test with inspector
npx @modelcontextprotocol/inspector --server-url http://localhost:8079/sse

# Test with claude code
claude mcp add --transport sse x-agent http://localhost:8079/sse
```

**To change system prompt**

1. Edit `agent.md`
2. Restart agent `docker restart core-x-agent`

**To clear the neo4j graph database**

```sql
MATCH (n) DETACH DELETE n
```
