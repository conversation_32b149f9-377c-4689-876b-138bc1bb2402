```mermaid
erDiagram
    EXPENSE {
        int id PK
        int user_id FK "references USER from user-management"
        string title
        string description
        decimal amount
        string currency "e.g. USD, EUR"
        int category_id FK
        date expense_date
        string receipt_url
        string status "pending, approved, rejected"
        int approved_by FK "user_id"
        datetime approved_at
        datetime created_at
        datetime updated_at
    }

    CATEGORY {
        int id PK
        string name "unique"
        string description
        string color
        string icon
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    BUDGET {
        int id PK
        int user_id FK "references USER from user-management"
        int category_id FK
        decimal amount
        string currency
        string period "monthly, quarterly, yearly"
        date start_date
        date end_date
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    PAYMENT_METHOD {
        int id PK
        int user_id FK "references USER from user-management"
        string name
        string type "credit_card, debit_card, cash, bank_transfer"
        string last_four
        boolean is_default
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    EXPENSE_PAYMENT {
        int id PK
        int expense_id FK
        int payment_method_id FK
        decimal amount
        datetime payment_date
    }

    TAG {
        int id PK
        string name "unique"
        string color
        datetime created_at
    }

    EXPENSE_TAG {
        int id PK
        int expense_id FK
        int tag_id FK
        datetime tagged_at
    }

    EXPENSE_ATTACHMENT {
        int id PK
        int expense_id FK
        string file_name
        string file_url
        string file_type
        int file_size
        datetime uploaded_at
    }

    CATEGORY ||--o{ EXPENSE : categorizes
    CATEGORY ||--o{ BUDGET : limits

    EXPENSE ||--o{ EXPENSE_PAYMENT : paid_by
    PAYMENT_METHOD ||--o{ EXPENSE_PAYMENT : used_in

    EXPENSE ||--o{ EXPENSE_TAG : tagged_with
    TAG ||--o{ EXPENSE_TAG : applies_to

    EXPENSE ||--o{ EXPENSE_ATTACHMENT : has
```
