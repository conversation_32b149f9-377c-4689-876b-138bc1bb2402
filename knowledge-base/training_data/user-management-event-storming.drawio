<mxfile host="65bd71144e">
    <diagram name="User Management Event Storming" id="user-mgmt-es-01">
        <mxGraphModel dx="2400" dy="1300" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3500" pageHeight="3200" background="#ffffff" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>

                <!-- Bounded Context: User Management -->
                <object label="User Management Context" type="context" id="Kx7mP2wQ">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#999999;swimlaneLine=0;fillColor=#F5F5F5;gradientColor=none;swimlaneFillColor=#f9f9f9;fontStyle=1;fontColor=#333333;glass=0;shadow=1;fontSize=20;" parent="1" vertex="1">
                        <mxGeometry x="40" y="40" width="3400" height="3080" as="geometry"/>
                    </mxCell>
                </object>

                <!-- ============================================ -->
                <!-- FLOW 1: User Account Creation & Onboarding -->
                <!-- ============================================ -->
                <object label="User Account Creation &amp; Onboarding" type="flow" id="Fn9BtYz3">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#E8F5E9;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#2E7D32;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="50" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: User List View -->
                <object label="User List View" type="readmodel" id="Rd4VcH8s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: User Management Dashboard -->
                <object label="User Management&lt;br&gt;Dashboard" type="ui" id="Mq3NpL6v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Admin/Finance User -->
                <object label="Admin/Finance&lt;br&gt;User" type="actor" id="Ht8WkJ5x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Create User Account -->
                <object label="Create User&lt;br&gt;Account" type="action" input="name: string&amp;#xa;email: string&amp;#xa;role: Role&amp;#xa;department: string&amp;#xa;position: string" success="userId: number" error="VALIDATION_ERROR&amp;#xa;DUPLICATE_EMAIL&amp;#xa;INTERNAL_ERROR" id="Bp7YqM2n">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: User Account Management -->
                <object label="User Account&lt;br&gt;Management" type="policy" id="Cw9XtN4p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: User Account Created -->
                <object label="User Account&lt;br&gt;Created" type="event" id="Dx5ZrP8t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Send Welcome Email -->
                <object label="Send Welcome&lt;br&gt;Email" type="reaction_policy" id="Em6AsQ9v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Email Service -->
                <object label="Email Service" type="external_system" id="Fq7BwL3m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Welcome Email Sent -->
                <object label="Welcome Email&lt;br&gt;Sent" type="event" id="Gt8CnM5p">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Fn9BtYz3" vertex="1">
                        <mxGeometry x="1390" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 1 -->
                <mxCell id="edgeVw8Km2Pd" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Rd4VcH8s" target="Mq3NpL6v" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeXn9Bw5Qr" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mq3NpL6v" target="Ht8WkJ5x" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeYp7Cm3Ts" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ht8WkJ5x" target="Bp7YqM2n" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeZq8Dn4Uv" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bp7YqM2n" target="Cw9XtN4p" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeAr9Ep5Vw" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cw9XtN4p" target="Dx5ZrP8t" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeBs8Fq6Wx" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dx5ZrP8t" target="Em6AsQ9v" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeCt9Gr7Xy" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Em6AsQ9v" target="Fq7BwL3m" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeDu8Hs8Yz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fq7BwL3m" target="Gt8CnM5p" edge="1" parent="Fn9BtYz3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 2: Email/Password Authentication -->
                <!-- ============================================ -->
                <object label="Email/Password Authentication" type="flow" id="Hw9JtM6z">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#E3F2FD;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#1565C0;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="420" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: Login Form Data -->
                <object label="Login Form&lt;br&gt;Data" type="readmodel" id="Jx7NqP9w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Login Page -->
                <object label="Login Page" type="ui" id="Ky8MrQ2x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Employee -->
                <object label="Employee" type="actor" id="Lz9NsR3y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Login with Email/Password -->
                <object label="Login with&lt;br&gt;Email/Password" type="action" input="email: string&amp;#xa;password: string" success="sessionToken: string&amp;#xa;userId: number" error="INVALID_CREDENTIALS&amp;#xa;ACCOUNT_LOCKED&amp;#xa;ACCOUNT_INACTIVE" id="Ma8OtS4z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Authentication Handler -->
                <object label="Authentication&lt;br&gt;Handler" type="policy" id="Nb9PuT5a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Login Succeeded -->
                <object label="Login&lt;br&gt;Succeeded" type="event" id="Oc7QvU6b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Create Session -->
                <object label="Create User&lt;br&gt;Session" type="reaction_policy" id="Pd8RwV7c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Session Created -->
                <object label="Session&lt;br&gt;Created" type="event" id="Qe9SxW8d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Hw9JtM6z" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 2 -->
                <mxCell id="edgeEv7TyX9e" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Jx7NqP9w" target="Ky8MrQ2x" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeFw8UzY2f" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ky8MrQ2x" target="Lz9NsR3y" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeGx9VzZ3g" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Lz9NsR3y" target="Ma8OtS4z" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeHy8WaA4h" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ma8OtS4z" target="Nb9PuT5a" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeIz9XbB5i" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Nb9PuT5a" target="Oc7QvU6b" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeJa7YcC6j" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Oc7QvU6b" target="Pd8RwV7c" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeKb8ZdD7k" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pd8RwV7c" target="Qe9SxW8d" edge="1" parent="Hw9JtM6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 3: Failed Login & Account Lockout -->
                <!-- ============================================ -->
                <object label="Failed Login &amp; Account Lockout" type="flow" id="Rf8KuN7a">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#FFEBEE;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#C62828;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="790" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Login Failed (from previous flow) -->
                <object label="Login Failed" type="event" id="Sg9LvO8b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Track Failed Attempts -->
                <object label="Track Failed&lt;br&gt;Attempts" type="reaction_policy" id="Th7MwP9c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Account Lockout Policy -->
                <object label="Account Lockout&lt;br&gt;Policy&lt;br&gt;(5 attempts)" type="policy" id="Ui8NxQ3d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Account Locked -->
                <object label="Account Locked&lt;br&gt;(30 min)" type="event" id="Vj9OyR4e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Send Lockout Notification -->
                <object label="Send Lockout&lt;br&gt;Notification" type="reaction_policy" id="Wk8PzS5f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Email Service -->
                <object label="Email Service" type="external_system" id="Xl9QaT6g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Lockout Notification Sent -->
                <object label="Lockout&lt;br&gt;Notification Sent" type="event" id="Ym7RbU7h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Rf8KuN7a" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 3 -->
                <mxCell id="edgeLc8AdE8l" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Sg9LvO8b" target="Th7MwP9c" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeMd9BeF9m" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Th7MwP9c" target="Ui8NxQ3d" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeNe8CfG2n" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ui8NxQ3d" target="Vj9OyR4e" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeOf9DgH3o" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vj9OyR4e" target="Wk8PzS5f" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgePg8EhI4p" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wk8PzS5f" target="Xl9QaT6g" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeQh9FiJ5q" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xl9QaT6g" target="Ym7RbU7h" edge="1" parent="Rf8KuN7a">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 4: Password Reset Request -->
                <!-- ============================================ -->
                <object label="Password Reset Request" type="flow" id="Zn8LwQ9r">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#FFF3E0;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#E65100;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="1160" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: Password Reset Form -->
                <object label="Password Reset&lt;br&gt;Form" type="readmodel" id="Ao7MxR2s">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Password Reset Page -->
                <object label="Password Reset&lt;br&gt;Page" type="ui" id="Bp8NyS3t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Employee -->
                <object label="Employee" type="actor" id="Cq9OzT4u">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Request Password Reset -->
                <object label="Request&lt;br&gt;Password Reset" type="action" input="email: string" success="resetToken: string" error="USER_NOT_FOUND&amp;#xa;INTERNAL_ERROR" id="Dr8PaU5v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Password Reset Handler -->
                <object label="Password Reset&lt;br&gt;Handler" type="policy" id="Es9QbV6w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Password Reset Requested -->
                <object label="Password Reset&lt;br&gt;Requested" type="event" id="Ft7RcW7x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Send Reset Link -->
                <object label="Send Reset Link&lt;br&gt;Email" type="reaction_policy" id="Gu8SdX8y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Email Service -->
                <object label="Email Service" type="external_system" id="Hv9TeY9z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Reset Link Sent -->
                <object label="Reset Link&lt;br&gt;Sent" type="event" id="Iw7UfZ2a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Zn8LwQ9r" vertex="1">
                        <mxGeometry x="1390" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 4 -->
                <mxCell id="edgeRi8GkJ6r" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ao7MxR2s" target="Bp8NyS3t" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeSj9HlK7s" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bp8NyS3t" target="Cq9OzT4u" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeTk8ImL8t" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cq9OzT4u" target="Dr8PaU5v" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeUl9JnM9u" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Dr8PaU5v" target="Es9QbV6w" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeVm8KoN2v" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Es9QbV6w" target="Ft7RcW7x" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeWn9LpO3w" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ft7RcW7x" target="Gu8SdX8y" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeXo8MqP4x" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gu8SdX8y" target="Hv9TeY9z" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeYp9NrQ5y" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hv9TeY9z" target="Iw7UfZ2a" edge="1" parent="Zn8LwQ9r">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 5: Complete Password Reset -->
                <!-- ============================================ -->
                <object label="Complete Password Reset" type="flow" id="Ap8NsU6z">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#F3E5F5;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#6A1B9A;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="1530" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: New Password Form -->
                <object label="New Password&lt;br&gt;Form" type="readmodel" id="Bq9OtV7a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Set New Password Page -->
                <object label="Set New&lt;br&gt;Password Page" type="ui" id="Cr7PuW8b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Employee -->
                <object label="Employee" type="actor" id="Ds8QvX9c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Reset Password -->
                <object label="Reset Password" type="action" input="resetToken: string&amp;#xa;newPassword: string&amp;#xa;confirmPassword: string" success="success: boolean" error="INVALID_TOKEN&amp;#xa;TOKEN_EXPIRED&amp;#xa;PASSWORD_WEAK&amp;#xa;PASSWORD_MISMATCH" id="Et9RwY2d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Password Validation Policy -->
                <object label="Password&lt;br&gt;Validation Policy&lt;br&gt;(8+ chars, complexity)" type="policy" id="Fu8SxZ3e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Password Changed -->
                <object label="Password&lt;br&gt;Changed" type="event" id="Gv9TyA4f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Reset Failed Attempts -->
                <object label="Reset Failed&lt;br&gt;Login Attempts" type="reaction_policy" id="Hw8UzB5g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Account Unlocked -->
                <object label="Account&lt;br&gt;Unlocked" type="event" id="Ix7VaC6h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Ap8NsU6z" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 5 -->
                <mxCell id="edgeZq8OsR6z" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bq9OtV7a" target="Cr7PuW8b" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeAr9PtS7a" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cr7PuW8b" target="Ds8QvX9c" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeBs8QuT8b" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ds8QvX9c" target="Et9RwY2d" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeCt9RvU9c" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Et9RwY2d" target="Fu8SxZ3e" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeDu8SwV2d" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fu8SxZ3e" target="Gv9TyA4f" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeEv9TxW3e" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gv9TyA4f" target="Hw8UzB5g" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeFw8UyX4f" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hw8UzB5g" target="Ix7VaC6h" edge="1" parent="Ap8NsU6z">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 6: Role Assignment & Permission Management -->
                <!-- ============================================ -->
                <object label="Role Assignment &amp; Permission Management" type="flow" id="Jy9MtW7b">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#E8F5E9;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#2E7D32;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="50" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: User Role View -->
                <object label="User Role View" type="readmodel" id="Kz7NuX8c">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Role Management UI -->
                <object label="Role Management&lt;br&gt;UI" type="ui" id="La8OvY9d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Admin User -->
                <object label="Admin User" type="actor" id="Mb9PwZ2e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Assign Role -->
                <object label="Assign Role" type="action" input="userId: number&amp;#xa;role: Role&amp;#xa;(Employee|Manager|Finance|CEO)" success="success: boolean" error="USER_NOT_FOUND&amp;#xa;INVALID_ROLE&amp;#xa;PERMISSION_DENIED" id="Nc8QxA3f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Role Management Policy -->
                <object label="Role Management&lt;br&gt;Policy" type="policy" id="Od9RyB4g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Role Assigned -->
                <object label="Role Assigned" type="event" id="Pe8SzC5h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Update User Permissions -->
                <object label="Update User&lt;br&gt;Permissions" type="reaction_policy" id="Qf9TaD6i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Permissions Updated -->
                <object label="Permissions&lt;br&gt;Updated" type="event" id="Rg7UbE7j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Jy9MtW7b" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 6 -->
                <mxCell id="edgeGw9UbF8k" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Kz7NuX8c" target="La8OvY9d" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeHx8VcG9l" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="La8OvY9d" target="Mb9PwZ2e" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeIy9WdH2m" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mb9PwZ2e" target="Nc8QxA3f" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeJz7XeI3n" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Nc8QxA3f" target="Od9RyB4g" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeKa8YfJ4o" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Od9RyB4g" target="Pe8SzC5h" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeLb9ZgK5p" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pe8SzC5h" target="Qf9TaD6i" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeMc8AhL6q" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Qf9TaD6i" target="Rg7UbE7j" edge="1" parent="Jy9MtW7b">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 7: Department Assignment -->
                <!-- ============================================ -->
                <object label="Department Assignment" type="flow" id="Sh8LxR9s">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#FFF9C4;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#F57F17;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="420" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: Department Hierarchy -->
                <object label="Department&lt;br&gt;Hierarchy" type="readmodel" id="Ti9MyS2t">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Department Mgmt UI -->
                <object label="Department&lt;br&gt;Management UI" type="ui" id="Uj7NzT3u">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Admin User -->
                <object label="Admin User" type="actor" id="Vk8OaU4v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Assign Department -->
                <object label="Assign&lt;br&gt;Department" type="action" input="userId: number&amp;#xa;departmentId: number&amp;#xa;managerId: number" success="success: boolean" error="USER_NOT_FOUND&amp;#xa;DEPARTMENT_NOT_FOUND&amp;#xa;INVALID_MANAGER" id="Wl9PbV5w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Department Assignment Policy -->
                <object label="Department&lt;br&gt;Assignment Policy" type="policy" id="Xm8QcW6x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Department Assigned -->
                <object label="Department&lt;br&gt;Assigned" type="event" id="Yn9RdX7y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Update Access Scope -->
                <object label="Update Access&lt;br&gt;Scope" type="reaction_policy" id="Zo7SeY8z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Access Scope Updated -->
                <object label="Access Scope&lt;br&gt;Updated" type="event" id="Ap8TfZ9a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Sh8LxR9s" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 7 -->
                <mxCell id="edgeNd9VgM7r" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ti9MyS2t" target="Uj7NzT3u" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeOe8WhN8s" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Uj7NzT3u" target="Vk8OaU4v" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgePf9XiO9t" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vk8OaU4v" target="Wl9PbV5w" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeQg8YjP2u" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wl9PbV5w" target="Xm8QcW6x" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeRh9ZkQ3v" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xm8QcW6x" target="Yn9RdX7y" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeSi8AlR4w" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yn9RdX7y" target="Zo7SeY8z" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeTj9BmS5x" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zo7SeY8z" target="Ap8TfZ9a" edge="1" parent="Sh8LxR9s">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 8: SSO Authentication (Google/Azure AD) -->
                <!-- ============================================ -->
                <object label="SSO Authentication (Google/Azure AD)" type="flow" id="Bq9NuW8t">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#E1F5FE;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#01579B;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="790" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: SSO Provider List -->
                <object label="SSO Provider&lt;br&gt;List" type="readmodel" id="Cr8OvX9u">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: SSO Login Page -->
                <object label="SSO Login&lt;br&gt;Page" type="ui" id="Ds9PwY2v">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Employee -->
                <object label="Employee" type="actor" id="Et7QxZ3w">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Initiate SSO Login -->
                <object label="Initiate SSO&lt;br&gt;Login" type="action" input="provider: string&amp;#xa;(Google|AzureAD)" success="redirectUrl: string" error="PROVIDER_NOT_CONFIGURED&amp;#xa;INTERNAL_ERROR" id="Fu8RyA4x">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: SSO Provider -->
                <object label="SSO Provider&lt;br&gt;(Google/Azure)" type="external_system" id="Gv9SzB5y">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: SSO Authentication Succeeded -->
                <object label="SSO Auth&lt;br&gt;Succeeded" type="event" id="Hw8TaC6z">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Create/Update User -->
                <object label="Create/Update&lt;br&gt;User Account" type="reaction_policy" id="Ix9UbD7a">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Session Created -->
                <object label="Session&lt;br&gt;Created" type="event" id="Jy7VcE8b">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Bq9NuW8t" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 8 -->
                <mxCell id="edgeUk8CnT6y" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Cr8OvX9u" target="Ds9PwY2v" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeVl9DoU7z" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ds9PwY2v" target="Et7QxZ3w" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeWm8EpV8a" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Et7QxZ3w" target="Fu8RyA4x" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeXn9FqW9b" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Fu8RyA4x" target="Gv9SzB5y" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeYo8GrX2c" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Gv9SzB5y" target="Hw8TaC6z" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeZp9HsY3d" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Hw8TaC6z" target="Ix9UbD7a" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeAq8ItZ4e" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ix9UbD7a" target="Jy7VcE8b" edge="1" parent="Bq9NuW8t">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 9: Session Management & Expiry -->
                <!-- ============================================ -->
                <object label="Session Management &amp; Expiry" type="flow" id="Kr7PxZ9c">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#FCE4EC;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#880E4F;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="1160" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: User Inactive (30 min) -->
                <object label="User Inactive&lt;br&gt;(30 min)" type="event" id="Ls8QyA2d">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Check Session Validity -->
                <object label="Check Session&lt;br&gt;Validity" type="reaction_policy" id="Mt9RzB3e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Session Timeout Policy -->
                <object label="Session Timeout&lt;br&gt;Policy&lt;br&gt;(30 min)" type="policy" id="Nu8SaC4f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Session Expired -->
                <object label="Session&lt;br&gt;Expired" type="event" id="Ov9TbD5g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Terminate Session -->
                <object label="Terminate&lt;br&gt;Session" type="reaction_policy" id="Pw8UcE6h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: User Logged Out -->
                <object label="User&lt;br&gt;Logged Out" type="event" id="Qx9VdF7i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Kr7PxZ9c" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 9 -->
                <mxCell id="edgeBr8JuA5f" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ls8QyA2d" target="Mt9RzB3e" edge="1" parent="Kr7PxZ9c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeCs9KvB6g" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Mt9RzB3e" target="Nu8SaC4f" edge="1" parent="Kr7PxZ9c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeDt8LwC7h" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Nu8SaC4f" target="Ov9TbD5g" edge="1" parent="Kr7PxZ9c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeEu9MxD8i" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ov9TbD5g" target="Pw8UcE6h" edge="1" parent="Kr7PxZ9c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeFv8NyE9j" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Pw8UcE6h" target="Qx9VdF7i" edge="1" parent="Kr7PxZ9c">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 10: Account Activation/Deactivation -->
                <!-- ============================================ -->
                <object label="Account Activation/Deactivation" type="flow" id="Tr8QzB3d">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#E0F2F1;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#004D40;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="1530" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: User Account Status -->
                <object label="User Account&lt;br&gt;Status" type="readmodel" id="Us9RaC4e">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: User Status Management -->
                <object label="User Status&lt;br&gt;Management" type="ui" id="Vt8SbD5f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Admin User -->
                <object label="Admin User" type="actor" id="Wu9TcE6g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Deactivate Account -->
                <object label="Deactivate&lt;br&gt;Account" type="action" input="userId: number&amp;#xa;reason: string" success="success: boolean" error="USER_NOT_FOUND&amp;#xa;ALREADY_INACTIVE&amp;#xa;PERMISSION_DENIED" id="Xv8UdF7h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Account Status Policy -->
                <object label="Account Status&lt;br&gt;Policy" type="policy" id="Yw9VeG8i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Account Deactivated -->
                <object label="Account&lt;br&gt;Deactivated" type="event" id="Zx8WfH9j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Revoke All Sessions -->
                <object label="Revoke All&lt;br&gt;Sessions" type="reaction_policy" id="Ay9XgI2k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Access Revoked -->
                <object label="Access&lt;br&gt;Revoked" type="event" id="Bz7YhJ3l">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Tr8QzB3d" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 10 -->
                <mxCell id="edgeGw8OzF2l" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Us9RaC4e" target="Vt8SbD5f" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeHx9PaG3m" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vt8SbD5f" target="Wu9TcE6g" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeIy8QbH4n" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wu9TcE6g" target="Xv8UdF7h" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeJz9RcI5o" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xv8UdF7h" target="Yw9VeG8i" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeKa8SdJ6p" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yw9VeG8i" target="Zx8WfH9j" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeLb9TeK7q" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zx8WfH9j" target="Ay9XgI2k" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeMc8UfL8r" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ay9XgI2k" target="Bz7YhJ3l" edge="1" parent="Tr8QzB3d">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 11: Password Expiry (90 Days) -->
                <!-- ============================================ -->
                <object label="Password Expiry (90 Days)" type="flow" id="Us8RbD4e">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#FFF8E1;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#F57F17;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="30" y="1900" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: 90 Days Since Last Password Change -->
                <object label="90 Days Since&lt;br&gt;Last Password&lt;br&gt;Change" type="event" id="Vt9ScE5f">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Check Password Age -->
                <object label="Check Password&lt;br&gt;Age" type="reaction_policy" id="Wu8TdF6g">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Password Expiry Policy -->
                <object label="Password Expiry&lt;br&gt;Policy&lt;br&gt;(90 days)" type="policy" id="Xv9UeG7h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Password Expired -->
                <object label="Password&lt;br&gt;Expired" type="event" id="Yw8VfH8i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Send Expiry Notification -->
                <object label="Send Expiry&lt;br&gt;Notification" type="reaction_policy" id="Zx9WgI9j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- External System: Email Service -->
                <object label="Email Service" type="external_system" id="Ay8XhJ2k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f7d0df;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Expiry Notification Sent -->
                <object label="Expiry&lt;br&gt;Notification Sent" type="event" id="Bz9YiK3l">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Us8RbD4e" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 11 -->
                <mxCell id="edgeNe8VgM9s" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Vt9ScE5f" target="Wu8TdF6g" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeOf9WhN2t" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wu8TdF6g" target="Xv9UeG7h" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgePg8XiO3u" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xv9UeG7h" target="Yw8VfH8i" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeQh9YjP4v" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yw8VfH8i" target="Zx9WgI9j" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeRi8ZkQ5w" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zx9WgI9j" target="Ay8XhJ2k" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeSj9AlR6x" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ay8XhJ2k" target="Bz9YiK3l" edge="1" parent="Us8RbD4e">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- ============================================ -->
                <!-- FLOW 12: Permission Check & Enforcement -->
                <!-- ============================================ -->
                <object label="Permission Check &amp; Enforcement" type="flow" id="Vt8SdF6g">
                    <mxCell style="swimlane;whiteSpace=wrap;html=1;strokeColor=#666666;swimlaneLine=0;fillColor=#EDE7F6;gradientColor=none;swimlaneFillColor=#ffffff;fontStyle=1;fontColor=#4A148C;glass=0;shadow=1;fontSize=16;" parent="Kx7mP2wQ" vertex="1">
                        <mxGeometry x="1680" y="1900" width="1620" height="340" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Read Model: User Session Context -->
                <object label="User Session&lt;br&gt;Context" type="readmodel" id="Wu9TeG7h">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#b5e7a0;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="30" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- UI: Protected Resource -->
                <object label="Protected&lt;br&gt;Resource" type="ui" id="Xv8UfH8i">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f5f6f8;strokeColor=#000000;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="200" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Actor: Any User -->
                <object label="Any User" type="actor" id="Yw9VgI9j">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fef9b9;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="370" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Action: Access Resource -->
                <object label="Access Resource" type="action" input="resourceId: string&amp;#xa;action: string&amp;#xa;(read|write|delete)" success="data: any" error="UNAUTHORIZED&amp;#xa;FORBIDDEN&amp;#xa;RESOURCE_NOT_FOUND" id="Zx8WhJ2k">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#88d6f6;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="540" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Policy: Permission Enforcement Policy -->
                <object label="Permission&lt;br&gt;Enforcement&lt;br&gt;Policy" type="policy" id="Ay9XiK3l">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#efd250;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="710" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Access Granted -->
                <object label="Access&lt;br&gt;Granted" type="event" id="Bz8YjL4m">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="880" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Reaction Policy: Log Access Event -->
                <object label="Log Access&lt;br&gt;Event" type="reaction_policy" id="Ca9ZkM5n">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#c0a3cf;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="1050" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Event: Access Logged -->
                <object label="Access&lt;br&gt;Logged" type="event" id="Db8AlN6o">
                    <mxCell style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f1a259;strokeColor=none;fontSize=14;fontStyle=0;rotation=0;shadow=1;align=center;" parent="Vt8SdF6g" vertex="1">
                        <mxGeometry x="1220" y="60" width="140" height="140" as="geometry"/>
                    </mxCell>
                </object>

                <!-- Connections for Flow 12 -->
                <mxCell id="edgeTk8BmO7y" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Wu9TeG7h" target="Xv8UfH8i" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeUl9CnP8z" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Xv8UfH8i" target="Yw9VgI9j" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeVm8DoQ9a" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Yw9VgI9j" target="Zx8WhJ2k" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeWn9EpR2b" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Zx8WhJ2k" target="Ay9XiK3l" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeXo8FqS3c" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ay9XiK3l" target="Bz8YjL4m" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeYp9GrT4d" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Bz8YjL4m" target="Ca9ZkM5n" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edgeZq8HsU5e" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;fontColor=#333333;curved=1;" source="Ca9ZkM5n" target="Db8AlN6o" edge="1" parent="Vt8SdF6g">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>

                <!-- Cross-flow connection: Login Failed event triggers Account Lockout flow -->
                <mxCell id="edgeCrossFlow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#FF5722;fontColor=#333333;curved=1;dashed=1;" source="Nb9PuT5a" target="Sg9LvO8b" edge="1" parent="Kx7mP2wQ">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="850" y="530"/>
                            <mxPoint x="850" y="920"/>
                        </Array>
                    </mxGeometry>
                </mxCell>

            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
