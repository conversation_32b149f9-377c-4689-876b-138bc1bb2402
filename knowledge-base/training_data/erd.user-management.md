```mermaid
erDiagram
    USER {
        int id PK "UUID/int primary key"
        string username "unique"
        string email "unique"
        string first_name
        string last_name
        string password_hash
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    ROLE {
        int id PK
        string name "unique"
        string description
        datetime created_at
        datetime updated_at
    }

    USER_ROLE {
        int id PK
        int user_id FK
        int role_id FK
        datetime assigned_at
        int assigned_by "user_id"
    }

    PERMISSION {
        int id PK
        string action "e.g. users.create"
        string resource "e.g. user, role"
        string description
        datetime created_at
    }

    ROLE_PERMISSION {
        int id PK
        int role_id FK
        int permission_id FK
        boolean allow
    }

    USER ||--o{ USER_ROLE : has
    ROLE ||--o{ USER_ROLE : contains

    ROLE ||--o{ ROLE_PERMISSION : has
    PERMISSION ||--o{ ROLE_PERMISSION : included_in
```
