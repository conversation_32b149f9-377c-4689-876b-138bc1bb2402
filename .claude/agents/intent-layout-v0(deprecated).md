# Create Flow Guideline: Drawer vs. Full Page

  | Component / Layout                | Instructions                                                                                        |
  | --------------------------------- | --------------------------------------------------------------------------------------------------- |
  | `@apps/v4/components/crud-drawers/` | When the user clicks **Create**, always open a drawer with an overlay instead of navigating away. |

  ⚠️ **Do not change the drawer's position or layout.** The drawer must always slide in from the right side, with header at the top and action buttons (Save / Cancel) fixed at the bottom.

  Use for **lightweight or contextual create actions**:

  * Low–medium complexity forms (<10 fields, single step)
  * Quick add flows without breaking workflow
  * Context preservation (user may need to see list/table in background)
  * Subordinate object creation (e.g. add task inside a project, note inside a patient record)
  * Frequent, inline actions
  * **Low risk** (see detailed definition below)

  **Example:** From a list of accounts, clicking "Create Contact" slides in a drawer with the contact form, always right-side slide-in, with fixed header + footer pattern.|

  | `@apps/v4/components/forms/create-multi-step-form-container.tsx` | When the user clicks **Create**, always navigate to a **dedicated full page** for the form.

  ⚠️ **Do not alter the page layout.** The page must always use the standard application layout: global header on top, page title + breadcrumb at the top-left, form body in main content area, action buttons in sticky footer.

  Use for **heavyweight creation flows**:

  * High complexity forms (>10 fields, multi-section, wizard-style)
  * Primary object creation (e.g. project, patient, invoice, insurance claim)
  * Rare but high-value actions
  * Step-by-step or onboarding style flows
  * Deep data entry requiring external references
  * **High risk** (see detailed definition below)

  **Example:** From the patients dashboard, clicking "Create Patient" navigates to a full-page patient creation form with multiple sections, always full-width page, respecting global app layout (header, breadcrumb, content, footer).|

  | `@source/apps/v4/components/table-view/` | **When to Render**
  - Default for admin/ops queues and bulk operations with structured datasets.
  - Data is dense & consistent schema (atomic fields: text, numbers, dates, enums, booleans).
  - Users must scan/compare many records side-by-side with sorting, filtering, and selection.
  - Large datasets (≥ 500 items; scales to 10k+ with pagination).
  - Visible attributes per item ≥ 6; target ≥ 30 rows per screen @ 1440px.

  ⚠️ **Strict Layout Rules (Do Not Change)**
  - Keep canonical table structure: TableViewToolbar at top, Table with sortable headers, TableViewPagination at bottom.
  - Built-in row selection with checkboxes (configurable via `enableRowSelection`).
  - Preserve column alignment and responsive design; avoid horizontal scroll unless required.
  - Use variants: "default", "compact", "spacious" for different density needs.
  - Maintain TanStack Table integration for state management.

  **Required Capabilities (Built-in)**
  - Column sorting (single/multi-sort) via TableViewColumnHeader dropdown.
  - Column visibility controls in TableViewToolbar (desktop only).
  - Search functionality with configurable search column and placeholder.
  - Advanced filtering with FilterOption arrays (dropdown checkboxes).
  - Pagination with configurable page sizes [10, 20, 30, 40, 50].
  - Row selection state management and bulk operations.
  - Loading states with skeleton placeholders (5 rows).
  - Empty states with "No results found" message.
  - Delete confirmation modal with customizable title/message.
  - Export functionality (CSV, Excel, PDF) when enabled.
  - Keyboard navigation and A11y semantics built-in.

  **Technical Implementation**
  - Main component: `<TableView>` with TypeScript generics `<TData, TValue>`.
  - Required props: `columns: ColumnDef[]`, `data: TData[]`.
  - Optional config: `TableViewConfig` with search, pageSize, row selection settings.
  - Toolbar: `TableViewToolbarProps` with filters, sorting, export options.
  - Built on TanStack Table v8 + shadcn/ui components.
  - Uses React context for delete functionality (`useTableViewDelete` hook).

  **Use/Do Not Use**
  - ✅ Use for: patient registries, invoice queues, account management, contact databases, inventory, appointment lists, claim processing, user management, department mangement, role management.
  - ❌ Avoid when items are content-rich (images, long descriptions) or variable schema — use listing-views instead.

  **Decision Conditions**
  - Dataset has ≥ 500 items with consistent schema structure.
  - Users need bulk operations (select multiple, batch actions, export).
  - Administrative/operational workflows requiring oversight and auditability.
  - Side-by-side comparison and multi-column sorting/filtering is essential.

  **Performance & Risk**
  - Virtualization handled by pagination (25 rows default); debounced search/filters.
  - Prefer for primary/high-risk entities (patients, invoices, claims) due to structured oversight.
  - Support view toggles between table/cards when both patterns are valid.|

  | `@source/apps/v4/components/listing-view-without-image.tsx` | **When to Render**
  - Use for structured data display where direct comparison and scanning is important.
  - Data has consistent schema with 4-8 key attributes per item.
  - Users need to compare items side-by-side (services, projects, environments).
  - Administrative workflows requiring clear status visibility.
  - Dataset size: 10-500 items with built-in search/filter.
  - Content is primarily text-based with simple status indicators.

  ⚠️ **Strict Layout Rules (Do Not Change)**
  - Maintain table structure: Search input above, table with fixed columns, row hover states.
  - Keep column widths: Name (200px), Type/Status/Owner/LastUpdated (auto), Actions (100px).
  - Preserve built-in search functionality with real-time filtering.
  - Status badges must use predefined color variants: deployed (green), available (blue), in-progress
  (yellow), error (red).
  - Row click interactions must be preserved (cursor pointer, hover effects).

  **Required Capabilities (Built-in)**
  - Real-time search across name, description, type, owner fields.
  - Status badge system with consistent color coding.
  - Row click handling with optional callback.
  - Empty state messaging.
  - Item count display.
  - Type badges with outline variant.
  - Description truncation with line-clamp-1.
  - Responsive table layout with horizontal scroll on mobile.

  **Technical Implementation**
  - Main component: `<ListingViewWithoutImage>` with `ListingItem[]` interface.
  - Required props: `items: ListingItem[]`.
  - Optional props: `onItemClick`, `onSearch`, `searchPlaceholder`, `emptyStateMessage`.
  - Built on shadcn/ui Table components with Badge system.
  - Uses React.useMemo for search performance.
  - Status types: "deployed" \| "available" \| "in-progress" \| "error".

  **Use/Do Not Use**
  - ✅ Use for: service listings, project management, environment status, infrastructure monitoring, system
  resources.
  - ❌ Avoid when: content is rich media, variable schema across items, need visual recognition,
  mobile-first workflows.|

  | `@source/apps/v4/components/listing-view-with-image.tsx` | **When to Render**
  - Use for content-rich items requiring visual recognition.
  - Items have images, icons, or visual identifiers.
  - Variable schema where items may have different attribute sets.
  - User research insights, content libraries, project galleries.
  - Dataset size: 5-200 items with visual browsing patterns.
  - Mobile-first workflows where card scanning is preferred.

  ⚠️ **Strict Layout Rules (Do Not Change)**
  - Maintain card anatomy: Header (icon + title + count badge), Content (description + badges), Footer
  (author + timestamp).
  - Preserve responsive grid: 1 col mobile, 2-4 cols desktop based on `columns` prop.
  - Keep card hover animations: shadow-md, -translate-y-1 transform.
  - Icon/image container must use gradient backgrounds from predefined cardColors array.
  - Maintain consistent card spacing (gap-6) and equal height cards.

  **Required Capabilities (Built-in)**
  - Responsive grid layout with configurable columns (1, 2, 3, 4).
  - Real-time search across title, description, category, author fields.
  - Visual identifier system: images, icons, or fallback initials.
  - Status badge system with consistent color coding.
  - Author attribution with avatar support.
  - Gradient background colors with automatic rotation.
  - Card hover interactions and click handling.
  - Clip/count badges for quantified content.
  - Content truncation: title (line-clamp-1), description (line-clamp-2).

  **Technical Implementation**
  - Main component: `<ListingViewWithImage>` with `ListingItemWithImage[]` interface.
  - Required props: `items: ListingItemWithImage[]`.
  - Optional props: `onItemClick`, `onSearch`, `columns`, `searchPlaceholder`, `emptyStateMessage`.
  - Built on shadcn/ui Card, Badge, Avatar components.
  - Status types: "active" \| "inactive" \| "pending" \| "completed".
  - Gradient colors: 6 predefined options rotating by index.
  - Responsive breakpoints: md (2 cols), lg (3 cols), xl (4 cols).

  **Use/Do Not Use**
  - ✅ Use for: research insights, content galleries, project portfolios, user-generated content, creative
  workflows.
  - ❌ Avoid when: need bulk operations, dense data comparison, administrative oversight, audit trails.|

  ---

  ## 🔹 Low Risk Definition

  Applies when mistakes can be easily undone and the impact is limited.
  **Indicators:**

  * Object is subordinate/secondary (e.g., note, tag, comment, quick task)
  * Errors are reversible with minimal impact (easy delete/edit)
  * Data is non-critical (not tied to billing, compliance, legal, or patient safety)
  * Frequent, repetitive actions where efficiency matters most
  * User can act while referencing background context

  **Examples:** Create Contact, add a note, assign a label/tag, quick reminder task.

  ---

  ## 🔹 High Risk Definition

  Applies when mistakes could have serious business, legal, or operational consequences.
  **Indicators:**

  * Object is a primary entity (e.g., patient, invoice, claim, contract)
  * Errors are difficult or impossible to reverse (cascading consequences)
  * Data is business-critical or legally binding
  * Rare but high-value actions requiring deliberate confirmation
  * Requires full attention, step-by-step guidance, or external references

  **Examples:** Create Patient, submit insurance claim, generate invoice, register new contract.

  ---

  ## Rule of Thumb

  * **Drawer = quick, contextual, subordinate, simple, low risk**
  * **Full Page = primary, complex, multi-step, focused, high risk**

  ---

  ## Strict Layout Rules

  * The agent **must not reposition or redesign** the drawer or page layout.
  * Only modify **content inside the form body** (fields, labels, steps).