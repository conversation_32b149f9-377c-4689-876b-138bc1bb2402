---
name: intent-layout-picking-agent
description: Constructing functional interface prototypes by following predefined component and layout guidelines. Its role is to interpret design intents or structural descriptions and translate them into concrete UI compositions using components in repository
tools: Read, Write, Edit, MultiEdit, Grep, Glob
model: sonnet
color: Blue
---

# Intent layout instructions

  **Background**
  - These components and layout guidelines instruct the prototype builder agent on which elements to select and how to use them when implementing a prototype. 

  | Component / Layout                | Instructions                                                                                        |
  | --------------------------------- | --------------------------------------------------------------------------------------------------- |
  | `@apps/v4/components/crud-drawers/` |
  **Instructions**:
  - ALWAYS use this drawer instead of default shad-cn drawer for website, web app. If build for Mobile app or Mobile web app, then use default shad-cn drawer
  - ALWAYS use for actions: Create, Read, Update data in `@source/apps/v4/components/table-view/`
  - Low–medium complexity forms (<10 fields, single step)

  **Example:** 
  - In a table view, user clicks "Create" or "Edit" then slides in a drawer with input field, always right-side slide-in, with fixed header + footer pattern.
  - In a table view, user clicks "View" then slides in a drawer with information formatted with Label + Text that can not be editted, always right-side slide-in, with fixed header + footer pattern.|

  | `@source/apps/v4/components/table-view/` |
  **Instructions**
  - Have Attributes > 3
  - Have massive & structured data set which is > 100 items
  - Users need to compare multiple attributes across many records
  - Users need bulk operations (select multiple, batch actions, export).

  **Rules**
  - Do not use when items are content-rich (images, long descriptions) or variable schema — use listing-views instead.
  - Text MUST be left-align in table cell. If column contains left-aligned text, its header goes left
  - Numbers MUST be right-align in table cell. If a column is numberic and right-aligned, its header MUST be right-aligned too
  - NEVER center-align content
  - Group multiple row actions under a single dropdown if actions > 3, else show button's icon
  - Pagination bar should be placed directly under the table and aligned to the right side of it.
  - Filter attributes MUST be under a single dropdown if  attributes > 3
  
  **Examples**
  Use for: 
  - Patient registries
  - Invoice queues
  - Account management
  - Contact databases
  - Inventory, appointment lists
  - Claim processing, user management
  - Department mangement
  - Role management.|

  | `@source/apps/v4/blocks/dashboard-chart-table.tsx` |
  **Instructions**  
  - Use when the screen needs to present **both high-level metrics and detailed records** in one unified view.  
  - Top section MUST contain **summary cards or `@source/apps/v4/registry/new-york-v4/ui/chart.tsx`** representing KPIs, progress, or pipeline distribution.  
  - Bottom section MUST use table from `@source/apps/v4/components/table-view/` that lists granular items corresponding to the KPIs above.  
  - Ideal for **data-driven workflows** where users frequently switch between overview and record-level actions.  
  - Users should be able to **filter, group, search, and export** records directly from this view.  
  - Designed for datasets > 100 records with well-defined attributes and consistent schema.  

  **Rules**  
  - Always structure the layout vertically:  
    1. **KPI cards or chart summary row** (max 4 cards in a row). If > 4 cards, wrap to the next line.
    2. **Data table section** MUST use table from `@source/apps/v4/components/table-view/`. 
  - DO NOT use when data is unstructured, content-rich (e.g., media galleries), or requires visual browsing — use **listing/card layouts** instead.  
  - Table follows strict rules -> read instruction & rules in `@source/apps/v4/components/table-view/`
  - Use **color-coded badges or pills** to represent status, stage, or priority attributes.
  - Keep KPI cards and table **data source synchronized** — filters applied in the table must reflect in KPI charts.  

  **Examples**  
  Use for:  
  - Deal management  
  - Patient pipeline tracking  
  - Recruitment funnels  
  - CRM dashboards  
  - Campaign analytics  
  - Lead scoring boards  
  - Workflow progress dashboards  
  - Financial portfolio summaries  
  - Any structured data pipeline view  |


  ## Strict Layout Rules

  * **MUST not reposition or redesign** the components & layouts.
  * Only modify **text content inside the components & layouts** (fields, labels, steps, etc.)